# Audio Quiz Genie Kids - Frontend Architecture

This document outlines the frontend architecture for the Audio Quiz Genie Kids application.

## Directory Structure

```
src/
├── core/                  # Core utilities and hooks
│   ├── animations/        # Animation utilities
│   ├── context/           # Context providers
│   ├── hooks/             # Custom hooks
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
│
├── components/            # UI components
│   ├── common/            # Reusable components
│   ├── layout/            # Layout components
│   ├── forms/             # Form components
│   ├── feedback/          # Loaders, alerts, toasts
│   ├── data-display/      # Tables, lists, cards
│   ├── navigation/        # Navigation components
│   └── ui/                # Base UI components (shadcn)
│
├── features/              # Feature-specific components and logic
│   ├── auth/              # Authentication-related components
│   ├── user-management/   # User management components
│   ├── tasks/             # Task-related components
│   └── learning/          # Learning-related components
│
├── pages/                 # Page components
│
├── services/              # API and data services
│   ├── api/               # API client and service classes
│   └── storage/           # Local storage services
│
├── store/                 # Redux store and slices
│   └── slices/            # Redux slices
│
└── styles/                # Global styles and theme
```

## Key Concepts

### Component Organization

Components are organized by function and reusability:

- **Core**: Base utilities and hooks used throughout the application
- **Components**: UI components organized by function
- **Features**: Feature-specific components and logic
- **Pages**: Page components that compose features and components
- **Services**: API and data services
- **Store**: Redux store and slices

### Theme System

The theme system provides consistent styling across the application:

- **Theme Provider**: Manages theme state and provides theme context
- **Theme Tokens**: Defines semantic color tokens for consistent usage
- **Dark Mode**: Supports light and dark mode with smooth transitions
- **Color Palettes**: Multiple color palettes for customization

### Animation System

The animation system provides consistent animations across the application:

- **Page Transitions**: Smooth transitions between pages
- **Staggered Children**: Staggered animations for lists of items
- **Micro-interactions**: Small animations for better user feedback
- **Loading States**: Animated loading states for better perceived performance

### Navigation System

The navigation system provides consistent navigation across the application:

- **Route Configuration**: Centralized route configuration
- **Protected Routes**: Routes that require authentication
- **Breadcrumbs**: Navigation context for better user orientation
- **Active Route Indicators**: Visual feedback for active routes

### State Management

State management is handled by a combination of:

- **Redux**: Global application state
- **React Query**: Server state
- **Context**: UI state
- **Local State**: Component-specific state

## Usage Examples

### Creating a New Page

1. Create a new page component in `src/pages/`
2. Use the `MainLayout` component for consistent layout
3. Use the `AnimatedPage` component for page transitions
4. Add the route to `src/routes/index.ts`
5. Add the route to `src/App.tsx`

Example:

```tsx
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import AnimatedPage from '@/components/common/AnimatedPage';

const NewPage: React.FC = () => {
  return (
    <MainLayout title="New Page" description="This is a new page">
      <AnimatedPage>
        <div>Page content goes here</div>
      </AnimatedPage>
    </MainLayout>
  );
};

export default NewPage;
```

### Using the Theme System

```tsx
import { useTheme } from '@/core/context/ThemeContext';
import { cn } from '@/lib/utils';

const MyComponent: React.FC = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <div className={cn(
      "p-4 rounded-lg",
      isDarkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"
    )}>
      Content
    </div>
  );
};
```

### Using Animations

```tsx
import AnimatedPage, { StaggeredChildren } from '@/components/common/AnimatedPage';

const MyComponent: React.FC = () => {
  return (
    <AnimatedPage>
      <StaggeredChildren>
        <div>Item 1</div>
        <div>Item 2</div>
        <div>Item 3</div>
      </StaggeredChildren>
    </AnimatedPage>
  );
};
```

### Using Protected Routes

```tsx
import ProtectedRoute from '@/routes/ProtectedRoute';

// In App.tsx
<Route 
  path="/protected-page" 
  element={
    <ProtectedRoute requiredRoles={['admin']}>
      <ProtectedPage />
    </ProtectedRoute>
  } 
/>
```

## Best Practices

1. **Component Organization**: Keep components organized by function and reusability
2. **Consistent Styling**: Use the theme system for consistent styling
3. **Animations**: Use the animation system for consistent animations
4. **State Management**: Use the appropriate state management solution for each use case
5. **Type Safety**: Use TypeScript for type safety
6. **Error Handling**: Handle errors gracefully with error boundaries and fallbacks
7. **Performance**: Optimize performance with memoization and code splitting
8. **Accessibility**: Ensure components are accessible with proper ARIA attributes
9. **Testing**: Write tests for components and logic
10. **Documentation**: Document components and logic with JSDoc comments
