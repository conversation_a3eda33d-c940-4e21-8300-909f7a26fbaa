import { useState, useEffect } from 'react';

export interface TourStep {
  target: string; // CSS selector for the target element
  title: string;
  content: string;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  disableOverlay?: boolean;
}

/**
 * Enhanced hook for guided tours with improved user experience
 */
export const useGuidedTour = (steps: TourStep[], tourId: string) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [hasCompletedTour, setHasCompletedTour] = useState(() => {
    return localStorage.getItem(`tour-${tourId}-completed`) === 'true';
  });
  
  // Check if this is the first time the user is visiting
  useEffect(() => {
    if (!hasCompletedTour && !isOpen) {
      // Delay the tour to allow the page to fully render
      const timer = setTimeout(() => {
        setIsOpen(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [hasCompletedTour, isOpen]);
  
  /**
   * Start the tour
   */
  const startTour = () => {
    setCurrentStep(0);
    setIsOpen(true);
  };
  
  /**
   * End the tour
   */
  const endTour = () => {
    setIsOpen(false);
    setCurrentStep(0);
  };
  
  /**
   * Go to the next step
   */
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeTour();
    }
  };
  
  /**
   * Go to the previous step
   */
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  /**
   * Go to a specific step
   * @param step The step index to go to
   */
  const goToStep = (step: number) => {
    if (step >= 0 && step < steps.length) {
      setCurrentStep(step);
    }
  };
  
  /**
   * Complete the tour
   */
  const completeTour = () => {
    setIsOpen(false);
    setHasCompletedTour(true);
    localStorage.setItem(`tour-${tourId}-completed`, 'true');
  };
  
  /**
   * Reset the tour (mark as not completed)
   */
  const resetTour = () => {
    setHasCompletedTour(false);
    localStorage.removeItem(`tour-${tourId}-completed`);
  };
  
  return {
    isOpen,
    currentStep,
    currentStepData: steps[currentStep],
    hasCompletedTour,
    startTour,
    endTour,
    nextStep,
    prevStep,
    goToStep,
    completeTour,
    resetTour
  };
};
