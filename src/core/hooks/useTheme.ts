import { useThemeContext } from '@/core/context/ThemeContext';

/**
 * Hook to access and modify the current theme
 * @returns Theme context with theme state and setter functions
 */
export const useTheme = () => {
  const context = useThemeContext();

  return {
    ...context,
    theme: context.isDarkMode ? 'dark' : 'light',
    setTheme: (theme: 'light' | 'dark') => {
      context.setMode(theme);
    }
  };
};
