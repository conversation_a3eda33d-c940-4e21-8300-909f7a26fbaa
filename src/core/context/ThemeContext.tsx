import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeMode, ThemeColor, applyTheme } from '@/styles/theme';

// Theme context interface
interface ThemeContextType {
  mode: ThemeMode;
  color: ThemeColor;
  setMode: (mode: ThemeMode) => void;
  setColor: (color: ThemeColor) => void;
  toggleMode: () => void;
  isDarkMode: boolean;
}

// Create context with default values
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider props
interface ThemeProviderProps {
  children: ReactNode;
  defaultMode?: ThemeMode;
  defaultColor?: ThemeColor;
}

/**
 * Theme provider component
 * Manages theme state and provides theme context to children
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultMode = 'system',
  defaultColor = 'nepali',
}) => {
  // Initialize theme state from localStorage or defaults
  const [mode, setModeState] = useState<ThemeMode>(() => {
    const storedMode = localStorage.getItem('theme-mode') as ThemeMode;
    return storedMode || defaultMode;
  });

  const [color, setColorState] = useState<ThemeColor>(() => {
    const storedColor = localStorage.getItem('theme-color') as ThemeColor;
    return storedColor || defaultColor;
  });

  // Calculate if dark mode is active
  const isDarkMode = mode === 'dark' || (mode === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);

  // Set mode and update localStorage
  const setMode = (newMode: ThemeMode) => {
    setModeState(newMode);
    localStorage.setItem('theme-mode', newMode);
  };

  // Set color and update localStorage
  const setColor = (newColor: ThemeColor) => {
    setColorState(newColor);
    localStorage.setItem('theme-color', newColor);
  };

  // Toggle between light and dark mode
  const toggleMode = () => {
    setMode(mode === 'light' ? 'dark' : 'light');
  };

  // Apply theme when mode or color changes
  useEffect(() => {
    applyTheme(mode, color);
  }, [mode, color]);

  // Listen for system theme changes
  useEffect(() => {
    if (mode !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = () => {
      applyTheme('system', color);
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [mode, color]);

  // Context value
  const value: ThemeContextType = {
    mode,
    color,
    setMode,
    setColor,
    toggleMode,
    isDarkMode,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

/**
 * Hook to use the theme context directly
 */
export const useThemeContext = (): ThemeContextType => {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }

  return context;
};

/**
 * Original useTheme hook - kept for backward compatibility
 * This will be used by components that haven't been migrated yet
 */
export const useTheme = useThemeContext;
