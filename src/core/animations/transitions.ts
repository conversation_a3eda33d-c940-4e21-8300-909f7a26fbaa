/**
 * Page transition animations
 * This file defines the animations used for page transitions
 */

import { Variants } from 'framer-motion';

// Direction type for transitions
export type TransitionDirection = 'up' | 'down' | 'left' | 'right' | 'fade';

// Page transition variants
export const pageTransitionVariants = (direction: TransitionDirection = 'up'): Variants => {
  // Base fade transition
  if (direction === 'fade') {
    return {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
    };
  }
  
  // Direction-based transitions
  const directionVariants: Record<Exclude<TransitionDirection, 'fade'>, { x: number; y: number }> = {
    up: { x: 0, y: 20 },
    down: { x: 0, y: -20 },
    left: { x: 20, y: 0 },
    right: { x: -20, y: 0 },
  };
  
  const { x, y } = directionVariants[direction];
  
  return {
    initial: { opacity: 0, x, y },
    animate: { opacity: 1, x: 0, y: 0 },
    exit: { opacity: 0, x, y },
  };
};

// Staggered children variants
export const staggeredChildrenVariants = {
  parent: {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
    exit: {
      opacity: 0,
      transition: {
        when: 'afterChildren',
        staggerChildren: 0.05,
        staggerDirection: -1,
      },
    },
  },
  child: {
    initial: { opacity: 0, y: 10 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 10 },
  },
};

// Modal transition variants
export const modalTransitionVariants: Variants = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 },
};

// Slide transition variants
export const slideTransitionVariants = (direction: 'left' | 'right' | 'up' | 'down'): Variants => {
  const directionMap = {
    left: { x: -100, y: 0 },
    right: { x: 100, y: 0 },
    up: { x: 0, y: -100 },
    down: { x: 0, y: 100 },
  };
  
  const { x, y } = directionMap[direction];
  
  return {
    initial: { opacity: 0, x, y },
    animate: { opacity: 1, x: 0, y: 0 },
    exit: { opacity: 0, x, y },
  };
};

// Transition timing configurations
export const transitionTimings = {
  fast: {
    duration: 0.2,
    ease: 'easeOut',
  },
  normal: {
    duration: 0.4,
    ease: 'easeOut',
  },
  slow: {
    duration: 0.6,
    ease: 'easeInOut',
  },
};
