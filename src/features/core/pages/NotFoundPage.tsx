import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';

/**
 * Enhanced 404 Not Found page with animations and theme support
 */
const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  const svgVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15,
        delay: 0.3
      }
    },
    float: {
      y: [0, -15, 0],
      transition: {
        y: {
          repeat: Infinity,
          duration: 3,
          ease: "easeInOut"
        }
      }
    }
  };

  return (
    <div className={cn(
      "min-h-screen flex flex-col items-center justify-center p-4",
      isDarkMode ? "bg-gray-900" : "bg-gray-50"
    )}>
      <motion.div 
        className="max-w-md w-full text-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div 
          className="mb-8"
          variants={svgVariants}
          initial="hidden"
          animate={["visible", "float"]}
        >
          <svg 
            className="w-32 h-32 mx-auto" 
            viewBox="0 0 24 24" 
            fill="none" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle 
              cx="12" 
              cy="12" 
              r="10" 
              stroke={isDarkMode ? "#f43f5e" : "#e11d48"} 
              strokeWidth="2" 
            />
            <path 
              d="M15 9L9 15M9 9L15 15" 
              stroke={isDarkMode ? "#f43f5e" : "#e11d48"} 
              strokeWidth="2" 
              strokeLinecap="round" 
            />
          </svg>
        </motion.div>
        
        <motion.h1 
          className={cn(
            "text-5xl font-bold mb-4",
            isDarkMode ? "text-white" : "text-gray-900"
          )}
          variants={itemVariants}
        >
          404
        </motion.h1>
        
        <motion.h2 
          className={cn(
            "text-2xl font-semibold mb-4",
            isDarkMode ? "text-gray-200" : "text-gray-800"
          )}
          variants={itemVariants}
        >
          Page Not Found
        </motion.h2>
        
        <motion.p 
          className={cn(
            "mb-8",
            isDarkMode ? "text-gray-300" : "text-gray-600"
          )}
          variants={itemVariants}
        >
          The page you are looking for doesn't exist or has been moved.
        </motion.p>
        
        <motion.div variants={itemVariants}>
          <Button
            className="bg-nepali-red hover:bg-nepali-maroon text-white"
            onClick={() => navigate('/')}
          >
            Go Home
          </Button>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default NotFoundPage;
