import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  LayoutList,
  LogOut,
  Mic,
  Moon,
  Sun,
  ChevronLeft,
  ChevronRight,
  Lock,
} from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { useSidebar } from '@/components/ui/sidebar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";

const SideNavigation: React.FC = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const { theme, setTheme } = useTheme();
  const { isCollapsed, toggleCollapsed } = useSidebar();
  const isDark = theme === 'dark';
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [passwords, setPasswords] = useState({ new: '', confirm: '' });

  const menuItems = [
    { path: '/dashboard', label: 'Dashboard', icon: Home },
    { path: '/begin-learning', label: 'Begin Learning', icon: Mic },
    { path: '/tasks', label: 'Tasks', icon: LayoutList },
  ];

  const isActive = (path: string) => location.pathname === path;

  const handlePasswordChange = async () => {
    if (passwords.new !== passwords.confirm) {
      toast.error("Passwords don't match!");
      return;
    }
    if (passwords.new.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }
    try {
      // Add your password change logic here
      toast.success("Password changed successfully!");
      setIsPasswordDialogOpen(false);
      setPasswords({ new: '', confirm: '' });
    } catch (error) {
      toast.error("Failed to change password");
    }
  };

  return (
    <Sidebar className="border-r border-border/50">
      <SidebarHeader className="p-2">
        <div className="flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center gap-2"
          >
            <div className={cn(
              'w-8 h-8 rounded-lg flex items-center justify-center bg-gradient-to-br',
              isDark ? 'from-blue-500 to-purple-600' : 'from-blue-600 to-purple-700',
              'shadow-lg'
            )}>
              <span className="text-white font-bold text-sm">NP</span>
            </div>
            <AnimatePresence>
              {!isCollapsed && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <h2 className="font-semibold text-sm">Nepali Learning</h2>
                  <p className="text-xs text-muted-foreground">Learn Fluent Nepali</p>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          <Button
            variant="ghost"
            size="icon"
            className={cn(
              'rounded-lg',
              isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
            )}
            onClick={toggleCollapsed}
          >
            <ChevronLeft className={cn(
              'size-4 transition-transform duration-200',
              isCollapsed && 'rotate-180'
            )} />
          </Button>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2">
        <nav className="space-y-1">
          {menuItems.map((item, index) => (
            <motion.div
              key={item.path}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link to={item.path}>
                <Button
                  variant="ghost"
                  className={cn(
                    'w-full justify-start gap-2 h-9 px-3 transition-all duration-200 text-sm',
                    isActive(item.path) && (isDark
                      ? 'bg-blue-500/20 text-blue-400 hover:bg-blue-500/30'
                      : 'bg-blue-50 text-blue-700 hover:bg-blue-100'
                    ),
                    !isActive(item.path) && 'hover:bg-accent'
                  )}
                >
                  <item.icon className={cn(
                    'size-4',
                    isActive(item.path) && 'text-current'
                  )} />
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.span
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        className="font-medium"
                      >
                        {item.label}
                      </motion.span>
                    )}
                  </AnimatePresence>
                </Button>
              </Link>
            </motion.div>
          ))}
        </nav>
      </SidebarContent>

      <SidebarFooter className="p-2 space-y-2">
        {user && (
          <div className="pt-2 border-t border-border/50">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className={cn(
                    'w-full justify-start gap-3 h-11',
                    isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                  )}
                >
                  <Avatar className="size-7 border-2 border-border">
                    <AvatarImage src={user.profile_picture} />
                    <AvatarFallback className="text-xs">
                      {user.full_name ?
                        user.full_name.split(' ').map(part => part[0]).join('').toUpperCase() :
                        'U'
                      }
                    </AvatarFallback>
                  </Avatar>
                  <AnimatePresence>
                    {!isCollapsed && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                        className="flex-1 text-left"
                      >
                        {user.full_name && (
                          <p className="font-medium text-sm truncate">
                            {user.full_name}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground truncate">
                          {user.email}
                        </p>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4" align="end">
                <div className="flex flex-col gap-4">
                  <div className="flex items-start gap-4">
                    <Avatar className="size-16 border-4 border-border">
                      <AvatarImage src={user.profile_picture} />
                      <AvatarFallback className="text-xl">
                        {user.full_name ?
                          user.full_name.split(' ').map(part => part[0]).join('').toUpperCase() :
                          'U'
                        }
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      {user.full_name && (
                        <h3 className="font-semibold">{user.full_name}</h3>
                      )}
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                      {user.role && (
                        <span className="inline-flex items-center px-2 py-1 mt-1 text-xs font-medium rounded-full bg-primary/10 text-primary">
                          {user.role}
                        </span>
                      )}
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="justify-start gap-2"
                    onClick={() => setIsPasswordDialogOpen(true)}
                  >
                    <Lock className="size-4" />
                    Change Password
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>
        )}

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              'rounded-xl',
              isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
            )}
            onClick={() => setTheme(isDark ? 'light' : 'dark')}
          >
            <AnimatePresence mode="wait">
              <motion.div
                key={theme}
                initial={{ opacity: 0, rotate: -180 }}
                animate={{ opacity: 1, rotate: 0 }}
                exit={{ opacity: 0, rotate: 180 }}
                transition={{ duration: 0.3 }}
              >
                {isDark ? <Sun className="size-4" /> : <Moon className="size-4" />}
              </motion.div>
            </AnimatePresence>
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className={cn(
              'rounded-xl ml-auto',
              isDark ? 'hover:bg-gray-800 text-red-400' : 'hover:bg-gray-100 text-red-500'
            )}
            onClick={logout}
          >
            <LogOut className="size-4" />
          </Button>
        </div>
      </SidebarFooter>

      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent className={cn(
          'sm:max-w-[425px]',
          isDark ? 'bg-gray-900' : 'bg-white'
        )}>
          <DialogHeader>
            <DialogTitle>Change Password</DialogTitle>
            <DialogDescription>
              Enter your new password below. Password must be at least 8 characters long.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-password">New Password</Label>
              <Input
                id="new-password"
                type="password"
                value={passwords.new}
                onChange={(e) => setPasswords(prev => ({ ...prev, new: e.target.value }))}
                placeholder="Enter new password"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirm Password</Label>
              <Input
                id="confirm-password"
                type="password"
                value={passwords.confirm}
                onChange={(e) => setPasswords(prev => ({ ...prev, confirm: e.target.value }))}
                placeholder="Confirm new password"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPasswordDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handlePasswordChange}>
              Change Password
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Sidebar>
  );
};

export default SideNavigation;
