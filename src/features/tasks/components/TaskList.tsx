import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTaskListService, TaskSetFilter, TaskSet } from '@/api/taskListService';
import TaskCard from './TaskCard';
import TaskFilter from './TaskFilter';
import TaskPagination from './TaskPagination';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

/**
 * Enhanced component for displaying a list of task sets with filtering, pagination, and animations
 */
const TaskList: React.FC = () => {
  // Task service
  const { fetchTaskSets: apiFetchTaskSets } = useTaskListService();
  const { isDarkMode } = useTheme();

  // Use useCallback to memoize the fetchTaskSets function
  const fetchTaskSets = useCallback(
    (filter: TaskSetFilter) => apiFetchTaskSets(filter),
    [apiFetchTaskSets]
  );

  // State
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [taskSets, setTaskSets] = useState<TaskSet[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Filter state
  const [filter, setFilter] = useState<TaskSetFilter>({
    page: 1,
    limit: 10,
    sort_by: 'created_at',
    sort_order: -1,
    // Initialize with empty values for other filters
    status: undefined,
    search: undefined,
    start_date: undefined,
    end_date: undefined
  });

  // Track if initial data has been loaded
  const dataLoadedRef = useRef(false);

  // Load task sets
  useEffect(() => {
    // Skip if data has already been loaded and filter hasn't changed
    if (dataLoadedRef.current && !loading) {
      return;
    }

    const loadTaskSets = async () => {
      setLoading(true);
      setError(null);

      try {
        console.log('Fetching task sets with filter:', filter);
        const response = await fetchTaskSets(filter);

        // Check if response and response.items exist
        if (response && Array.isArray(response.items)) {
          setTaskSets(response.items);
          setTotalItems(response.total || 0);
          setTotalPages(response.pages || 0);
          dataLoadedRef.current = true;
        } else {
          console.error('Invalid API response format:', response);
          setTaskSets([]);
          setTotalItems(0);
          setTotalPages(0);
          setError('Invalid response format from server');
        }
      } catch (err) {
        console.error('Error fetching task sets:', err);
        setError(err instanceof Error ? err.message : 'Failed to load task sets');
        setTaskSets([]);
        setTotalItems(0);
        setTotalPages(0);
      } finally {
        setLoading(false);
      }
    };

    loadTaskSets();
  }, [filter, fetchTaskSets, loading]);

  // Handle filter change
  const handleFilterChange = (newFilter: TaskSetFilter) => {
    dataLoadedRef.current = false; // Reset to force reload
    setFilter(newFilter);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    dataLoadedRef.current = false; // Reset to force reload
    setFilter({ ...filter, page });
  };

  // Handle page size change
  const handlePageSizeChange = (limit: number) => {
    dataLoadedRef.current = false; // Reset to force reload
    setFilter({ ...filter, limit, page: 1 });
  };

  // Render loading skeletons
  const renderSkeletons = () => {
    return Array(filter.limit).fill(0).map((_, index) => (
      <div key={index} className={cn(
        "p-4 border rounded-lg",
        isDarkMode ? "border-gray-700" : "border-gray-200"
      )}>
        <Skeleton className={cn(
          "h-6 w-3/4 mb-2",
          isDarkMode ? "bg-gray-700" : "bg-gray-200"
        )} />
        <Skeleton className={cn(
          "h-4 w-1/4 mb-4",
          isDarkMode ? "bg-gray-700" : "bg-gray-200"
        )} />
        <Skeleton className={cn(
          "h-4 w-full mb-2",
          isDarkMode ? "bg-gray-700" : "bg-gray-200"
        )} />
        <Skeleton className={cn(
          "h-4 w-full mb-4",
          isDarkMode ? "bg-gray-700" : "bg-gray-200"
        )} />
        <Skeleton className={cn(
          "h-2 w-full mb-4",
          isDarkMode ? "bg-gray-700" : "bg-gray-200"
        )} />
        <div className="flex justify-between">
          <Skeleton className={cn(
            "h-4 w-1/4",
            isDarkMode ? "bg-gray-700" : "bg-gray-200"
          )} />
          <Skeleton className={cn(
            "h-4 w-1/4",
            isDarkMode ? "bg-gray-700" : "bg-gray-200"
          )} />
        </div>
      </div>
    ));
  };

  return (
    <div className="flex flex-col h-[calc(100vh-120px)]">
      {/* Fixed Header - Filter section */}
      <div className={cn(
        "sticky top-0 z-10 pt-4 px-4",
        isDarkMode ? "bg-gray-900" : "bg-white"
      )}>
        <TaskFilter filter={filter} onFilterChange={handleFilterChange} />

        {/* Error message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </motion.div>
        )}
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-grow overflow-y-auto px-4 py-2">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {loading ? (
            renderSkeletons()
          ) : taskSets && taskSets.length > 0 ? (
            taskSets.map((task, index) => (
              <motion.div
                key={task.id || index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <TaskCard task={task} />
              </motion.div>
            ))
          ) : (
            <div className={cn(
              "col-span-full text-center py-12",
              isDarkMode ? "text-gray-400" : "text-gray-500"
            )}>
              <p>No task sets found</p>
              <p className={cn(
                "text-sm mt-2",
                isDarkMode ? "text-gray-500" : "text-gray-400"
              )}>Try adjusting your filters</p>
            </div>
          )}
        </motion.div>
      </div>

      {/* Fixed Footer - Pagination */}
      <div className={cn(
        "sticky bottom-0 z-10 border-t px-4 py-3",
        isDarkMode ? "bg-gray-900 border-gray-700" : "bg-white border-gray-200"
      )}>
        {!loading && taskSets && taskSets.length > 0 ? (
          <TaskPagination
            currentPage={filter.page}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={filter.limit}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        ) : (
          <div className="h-10"></div> // Empty space to maintain footer height when no pagination
        )}
      </div>
    </div>
  );
};

export default TaskList;
