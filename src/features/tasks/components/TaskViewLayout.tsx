import React, { ReactNode } from 'react';
import SideNavigation from '@/features/navigation/components/SideNavigation';
import { motion } from 'framer-motion';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';

interface TaskViewLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
}

/**
 * Enhanced task view layout with animations and theme support
 */
const TaskViewLayout: React.FC<TaskViewLayoutProps> = ({ 
  children, 
  title = 'Task View',
  description = 'Complete your learning tasks'
}) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  return (
    <div className="flex min-h-screen">
      <SideNavigation />
      
      <motion.main 
        className="flex-1 overflow-auto"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div 
          className={cn(
            "p-4 border-b",
            isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"
          )}
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <h1 className={cn(
            "text-2xl font-bold",
            isDarkMode ? "text-white" : "text-gray-900"
          )}>
            {title}
          </h1>
          {description && (
            <p className={cn(
              "text-sm mt-1",
              isDarkMode ? "text-gray-300" : "text-gray-500"
            )}>
              {description}
            </p>
          )}
        </motion.div>
        
        <div className="p-4">
          {children}
        </div>
      </motion.main>
    </div>
  );
};

export default TaskViewLayout;
