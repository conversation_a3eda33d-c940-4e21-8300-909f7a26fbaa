import React from 'react';
import TaskItem from './TaskItem';
import { Task, QuestionObject } from '@/api/taskService';

/**
 * Demo component to showcase the new TaskItem functionality
 * This demonstrates how the component handles the new question object structure
 */
const TaskItemDemo: React.FC = () => {
  // Example task with new question object structure
  const sampleTaskWithQuestionObject: Task = {
    id: '6836ddbdf86052962c8f04d4',
    type: 'single_choice',
    question: {
      text: 'के भनेको?',
      translated_text: 'What was said?',
      options: {
        a: 'फिलिम',
        b: 'किताब',
        c: 'खाना'
      },
      answer_hint: 'फिलिम',
      media_url: null
    } as QuestionObject,
    correct_answer: {
      value: 'a',
      type: 'single'
    },
    user_answer: null,
    status: 'pending',
    result: null
  };

  // Example task with legacy string question (for backward compatibility)
  const sampleTaskWithStringQuestion: Task = {
    id: 'legacy-task-id',
    type: 'single_choice',
    question: 'This is a legacy string question?',
    options: ['Option A', 'Option B', 'Option C'],
    status: 'pending'
  };

  // Example task without translation (no translate button should appear)
  const sampleTaskWithoutTranslation: Task = {
    id: 'no-translation-task',
    type: 'single_choice',
    question: {
      text: 'Question without translation',
      translated_text: 'Question without translation', // Same as original
      options: {
        a: 'Option 1',
        b: 'Option 2',
        c: 'Option 3'
      },
      answer_hint: 'Option 1',
      media_url: null
    } as QuestionObject,
    status: 'pending'
  };

  return (
    <div className="p-6 space-y-8 max-w-4xl mx-auto">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">TaskItem Component Demo</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Demonstrating the new question object structure with translation support
        </p>
      </div>

      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold mb-3">1. Task with Translation Support</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            This task has both original text (Nepali) and translated text (English).
            Click the "Translate" button to toggle between them.
          </p>
          <TaskItem
            task={sampleTaskWithQuestionObject}
            onAnswerSubmitted={(taskId, isCorrect) => {
              console.log('Answer submitted:', { taskId, isCorrect });
              console.log('Expected: Single choice should submit key like "a", multiple choice should submit array like ["a", "b"]');
            }}
          />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">2. Multiple Choice Task</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            This demonstrates multiple choice selection. Should submit array of keys like ["a", "c"].
          </p>
          <TaskItem
            task={{
              ...sampleTaskWithQuestionObject,
              id: 'multiple-choice-demo',
              type: 'multiple_choice'
            }}
            onAnswerSubmitted={(taskId, isCorrect) => {
              console.log('Multiple choice answer submitted:', { taskId, isCorrect });
              console.log('Expected: Should submit array of keys like ["a", "c"]');
            }}
          />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">3. Legacy Task (Backward Compatibility)</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            This task uses the old string question format. No translate button should appear.
          </p>
          <TaskItem
            task={sampleTaskWithStringQuestion}
            onAnswerSubmitted={(taskId, isCorrect) => {
              console.log('Legacy answer submitted:', { taskId, isCorrect });
            }}
          />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">4. Task without Translation</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            This task has the same text for original and translated versions.
            No translate button should appear.
          </p>
          <TaskItem
            task={sampleTaskWithoutTranslation}
            onAnswerSubmitted={(taskId, isCorrect) => {
              console.log('No translation answer submitted:', { taskId, isCorrect });
            }}
          />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">5. Already Submitted Task (Correct)</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            This task is already submitted with a correct answer. Shows score 10/10 pt and green highlighting.
          </p>
          <TaskItem
            task={{
              ...sampleTaskWithQuestionObject,
              id: 'submitted-correct-demo',
              user_answer: 'a', // User selected option 'a'
              result: 'correct',
              submitted: true,
              status: 'completed',
              submitted_at: '2025-05-28T13:38:35.184000',
              total_score: 10,
              scored: 10
            }}
            onAnswerSubmitted={(taskId, isCorrect) => {
              console.log('This should not be called for submitted tasks');
            }}
          />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">6. Already Submitted Task (Incorrect)</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            This task is already submitted with an incorrect answer. Shows score 0/10 pt and red highlighting.
          </p>
          <TaskItem
            task={{
              ...sampleTaskWithQuestionObject,
              id: 'submitted-incorrect-demo',
              user_answer: 'b', // User selected option 'b' (incorrect)
              result: 'incorrect',
              submitted: true,
              status: 'completed',
              submitted_at: '2025-05-28T13:38:35.184000',
              total_score: 10,
              scored: 0
            }}
            onAnswerSubmitted={(taskId, isCorrect) => {
              console.log('This should not be called for submitted tasks');
            }}
          />
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-3">7. Partial Score Example</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            This task shows a partial score example (5/10 pt) matching your API response format.
          </p>
          <TaskItem
            task={{
              ...sampleTaskWithQuestionObject,
              id: 'partial-score-demo',
              user_answer: 'a',
              result: 'correct',
              submitted: true,
              status: 'completed',
              submitted_at: '2025-05-28T13:38:35.184000',
              total_score: 10,
              scored: 5
            }}
            onAnswerSubmitted={(taskId, isCorrect) => {
              console.log('This should not be called for submitted tasks');
            }}
          />
        </div>
      </div>

      <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="font-semibold mb-2">Key Features Demonstrated:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>✅ Handles new question object structure with text, translated_text, and options</li>
          <li>✅ Shows translate button only when translation is available and different from original</li>
          <li>✅ Toggles between original and translated text</li>
          <li>✅ Extracts options from question object instead of legacy options array</li>
          <li>✅ Maintains backward compatibility with string questions</li>
          <li>✅ Fixes the "Objects are not valid as a React child" error</li>
          <li>✅ <strong>Submits option keys (a, b, c) instead of values for single choice</strong></li>
          <li>✅ <strong>Submits array of option keys ([a, b, c]) for multiple choice</strong></li>
          <li>✅ Displays option values to users while tracking keys internally</li>
          <li>✅ <strong>Shows previously submitted answers when task.submitted is true</strong></li>
          <li>✅ <strong>Prevents re-selection and re-submission of already submitted tasks</strong></li>
          <li>✅ <strong>Visual feedback: Green for correct, Red for incorrect submitted answers</strong></li>
          <li>✅ <strong>Disabled state for all options when task is already submitted</strong></li>
          <li>✅ <strong>Displays individual task score as "scored/total_score pt" format</strong></li>
          <li>✅ <strong>Uses task.scored and task.total_score from API response</strong></li>
          <li>✅ <strong>Shows green dot for scored > 0, gray dot for scored = 0</strong></li>
        </ul>
      </div>
    </div>
  );
};

export default TaskItemDemo;
