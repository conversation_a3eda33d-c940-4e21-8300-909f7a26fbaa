import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TaskSet } from '@/api/taskListService';
import { fetchTaskSetScore, ScoreResponse } from '@/api/taskService';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@/core/hooks/useTheme';
import { useAuth } from '@/context/AuthContext';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';

interface TaskCardProps {
  task: TaskSet;
}

/**
 * Enhanced component for displaying a task set as a card with animations and theme support
 */
const TaskCard: React.FC<TaskCardProps> = ({ task }) => {
  const navigate = useNavigate();
  const { isDarkMode } = useTheme();
  const { user } = useAuth();

  // Score state
  const [taskSetScore, setTaskSetScore] = useState<ScoreResponse | null>(null);
  const [loadingScore, setLoadingScore] = useState(false);

  // Fetch task set score
  const fetchScore = async () => {
    const taskSetId = task.id || task._id;
    if (!taskSetId || !user) return;

    try {
      setLoadingScore(true);
      const scoreData = await fetchTaskSetScore(taskSetId, user);
      console.log('Task set score data for card:', scoreData);
      setTaskSetScore(scoreData);
    } catch (error) {
      console.warn('Could not fetch task set score for card:', error);
      // Don't set error state, just use fallback
    } finally {
      setLoadingScore(false);
    }
  };

  // Fetch score when component mounts or task changes
  useEffect(() => {
    fetchScore();
  }, [task.id, task._id, user]);

  // Format date to a readable format
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Calculate completion percentage
  const taskCount = task.tasks ? task.tasks.length : 0;
  const completedCount = task.completed_tasks || 0;
  const completionPercentage = taskCount > 0
    ? Math.round((completedCount / taskCount) * 100)
    : 0;

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    if (!status) return 'default';

    switch (status.toLowerCase()) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'info';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Handle card click
  const handleCardClick = () => {
    if (task && (task.id || task._id)) {
      // Navigate to task detail page
      navigate(`/tasks/${task.id || task._id}`);
    }
  };

  // If task is undefined or missing required properties, show a placeholder
  if (!task || (!task.id && !task._id)) {
    return (
      <Card className={cn(
        "w-full",
        isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
      )}>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-bold">Task data unavailable</CardTitle>
        </CardHeader>
        <CardContent>
          <p className={cn(
            "text-sm",
            isDarkMode ? "text-gray-400" : "text-gray-500"
          )}>Unable to display this task</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={cn(
        "overflow-hidden hover:shadow-md transition-shadow cursor-pointer",
        isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
      )}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{task.input_content || 'Task Set'}</CardTitle>
          <Badge variant={getStatusVariant(task.status || '')}>
            {task.status ? task.status.replace(/_/g, ' ') : 'pending'}
          </Badge>
        </div>
        <CardDescription>
          Created: {formatDate(task.created_at)}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <p className={cn(
          "text-sm font-medium mb-4",
          isDarkMode ? "text-gray-300" : "text-gray-600"
        )}>
          {task.input_type === 'audio' ? 'Audio Task' : task.input_type || 'Task'}
        </p>

        <div className="mb-1">
          <div className={cn(
            "text-xs mb-1",
            isDarkMode ? "text-gray-400" : "text-gray-500"
          )}>Completion</div>
          <Progress
            value={completionPercentage}
            className={cn(
              isDarkMode ? "bg-gray-700" : "bg-gray-200"
            )}
          />
        </div>

        <div className={cn(
          "flex justify-between text-sm mt-4",
          isDarkMode ? "text-gray-300" : "text-gray-600"
        )}>
          <div>Tasks: {completedCount ?? 0}/{taskCount ?? 0}</div>
          <div className="flex items-center">
            {loadingScore ? (
              <>
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
                <span>Loading...</span>
              </>
            ) : taskSetScore ? (
              <span>Score: {taskSetScore.score ?? 0}/{taskSetScore.total_score ?? taskSetScore.max_score ?? 0}</span>
            ) : task.scored !== undefined && task.max_score !== undefined ? (
              <span>Score: {task.scored ?? 0}/{task.max_score ?? 60}</span>
            ) : (
              <span>Score: 0/60</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskCard;
