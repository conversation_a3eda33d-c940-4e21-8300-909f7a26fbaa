import React, { useState, useRef, useCallback, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Task, QuestionObject, submitTaskAnswer, fetchTaskItemScore, ScoreResponse } from '@/api/taskService';
import { uploadAudioFile } from '@/api/fileService';
import { useRecorder } from '@/hooks/useAudioRecorder';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, CheckCircle, XCircle, Mic, MicOff, RefreshCw, Languages, Expand, X, ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useTheme } from '@/core/hooks/useTheme';

interface TaskItemProps {
  task: Task;
  onAnswerSubmitted?: (taskId: string, isCorrect: boolean) => void;
  onNext?: () => void;
  score?: number; // Keep for backward compatibility
  maxScore?: number; // Keep for backward compatibility
  // Navigation props
  onPrevious?: () => void;
  showPrevious?: boolean;
  showNext?: boolean;
  isFirstTask?: boolean;
  isLastTask?: boolean;
  isLoadingTasks?: boolean;
}

/**
 * Enhanced TaskItem component with animations and theme support
 */
const TaskItem: React.FC<TaskItemProps> = ({
  task,
  onAnswerSubmitted,
  onNext,
  score,
  onPrevious,
  showPrevious = true,
  showNext = true,
  isFirstTask = false,
  isLastTask = false,
  isLoadingTasks = false
}) => {
  const { user } = useAuth();
  const { theme } = useTheme();
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionResult, setSubmissionResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [textAnswer, setTextAnswer] = useState<string>('');
  const [showTranslation, setShowTranslation] = useState(false);
  const [showImagePreview, setShowImagePreview] = useState(false);

  // Score states
  const [taskScore, setTaskScore] = useState<ScoreResponse | null>(null);
  const [loadingScore, setLoadingScore] = useState(false);

  // Audio recording states
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Refs
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Handle recording stopped
  const handleRecordingStopped = useCallback(() => {
    console.log('Recording stopped in TaskItem');
  }, []);

  // Use the recorder hook
  const {
    recording: isRecording,
    audioUrl: recordedAudioUrl,
    startRecording,
    stopRecording
  } = useRecorder({
    onRecordingStopped: handleRecordingStopped
  });

  // Helper functions for question object handling
  const isQuestionObject = (question: string | QuestionObject | undefined): question is QuestionObject => {
    return typeof question === 'object' && question !== null && 'text' in question;
  };

  const getQuestionText = (): string => {
    if (!task.question) return '';

    if (isQuestionObject(task.question)) {
      return showTranslation ? task.question.translated_text : task.question.text;
    }

    return task.question;
  };

  const getQuestionOptions = (): string[] => {
    if (isQuestionObject(task.question)) {
      // Convert options object to array of values for display
      const options = task.question.options;
      return Object.keys(options).sort().map(key => options[key]);
    }

    // Fallback to legacy options array
    return task.options || [];
  };

  const getQuestionOptionsWithKeys = (): Array<{key: string, value: string}> => {
    if (isQuestionObject(task.question)) {
      // Return array of {key, value} pairs for new question format
      const options = task.question.options;
      return Object.keys(options).sort().map(key => ({
        key,
        value: options[key]
      }));
    }

    // For legacy format, create keys as indices
    const legacyOptions = task.options || [];
    return legacyOptions.map((value, index) => ({
      key: String.fromCharCode(97 + index), // 'a', 'b', 'c', etc.
      value
    }));
  };

  const getOptionKeyFromValue = (value: string): string => {
    if (isQuestionObject(task.question)) {
      const options = task.question.options;
      const entry = Object.entries(options).find(([_, optionValue]) => optionValue === value);
      return entry ? entry[0] : value;
    }

    // For legacy format, find index and convert to letter
    const legacyOptions = task.options || [];
    const index = legacyOptions.indexOf(value);
    return index >= 0 ? String.fromCharCode(97 + index) : value;
  };

  const getOptionValueFromKey = (key: string): string => {
    if (isQuestionObject(task.question)) {
      return task.question.options[key] || key;
    }

    // For legacy format, convert letter to index
    const index = key.charCodeAt(0) - 97;
    const legacyOptions = task.options || [];
    return legacyOptions[index] || key;
  };

  const hasTranslation = (): boolean => {
    return isQuestionObject(task.question) &&
           task.question.translated_text &&
           task.question.translated_text !== task.question.text;
  };

  // Fetch task item score using the new API
  const fetchTaskScore = async () => {
    const taskId = task.id || task._id;
    if (!taskId || !user) return;

    try {
      setLoadingScore(true);
      const scoreData = await fetchTaskItemScore(taskId, user);
      console.log('Task item score data:', scoreData);
      setTaskScore(scoreData);
    } catch (error) {
      console.warn('Could not fetch task item score:', error);
      // Don't set error state, just use fallback
    } finally {
      setLoadingScore(false);
    }
  };

  const getTaskScore = (): { scored: number; total: number } => {
    // Use the new API score if available
    if (taskScore) {
      return {
        scored: taskScore.score ?? 0, // Use nullish coalescing to handle null/undefined
        total: taskScore.max_score ?? 10
      };
    }

    // Use task's individual score if available (legacy)
    if (task.scored !== undefined && task.total_score !== undefined) {
      return {
        scored: task.scored ?? 0,
        total: task.total_score ?? 10
      };
    }

    // Fallback to props for backward compatibility
    return { scored: score ?? 0, total: 10 };
  };

  // Reset state when task changes
  useEffect(() => {
    // If task is already submitted, set the user's previous answer
    if (task.submitted && task.user_answer) {
      if (Array.isArray(task.user_answer)) {
        // Multiple choice - user_answer is already an array of keys
        setSelectedOptions(task.user_answer);
      } else {
        // Single choice - user_answer is a single key
        setSelectedOptions([task.user_answer]);
      }

      // Set submission result if available
      if (task.result) {
        setSubmissionResult({
          is_correct: task.result === 'correct',
          feedback: task.result === 'correct' ? 'Correct!' : 'Incorrect'
        });
      }
    } else {
      // Reset to empty state for new tasks
      setSelectedOptions([]);
      setSubmissionResult(null);
    }

    setError(null);
    setIsSubmitting(false);
    setIsUploading(false);
    setUploadProgress(0);
    setShowTranslation(false); // Reset translation state

    const taskId = task.id || task._id;
    console.log('Task changed, state reset. Task ID:', taskId, 'Submitted:', task.submitted);

    // Fetch task score when task changes
    fetchTaskScore();
  }, [task.id, task._id, task.submitted, task.user_answer, task.result]);

  // Handle single choice selection
  const handleSingleChoiceSelect = (optionValue: string) => {
    if (isSubmitting || task.submitted) return; // Prevent selection if already submitted
    const optionKey = getOptionKeyFromValue(optionValue);
    setSelectedOptions([optionKey]);
  };

  // Handle multiple choice selection
  const handleMultipleChoiceSelect = (optionValue: string) => {
    if (isSubmitting || task.submitted) return; // Prevent selection if already submitted
    const optionKey = getOptionKeyFromValue(optionValue);

    setSelectedOptions(prev => {
      if (prev.includes(optionKey)) {
        return prev.filter(item => item !== optionKey);
      } else {
        return [...prev, optionKey];
      }
    });
  };

  // Handle single choice submission
  const handleSingleChoiceSubmit = async () => {
    if (isSubmitting || selectedOptions.length === 0 || task.submitted) return;
    console.log('Submitting single choice answer:', selectedOptions[0]);
    await submitAnswer(selectedOptions[0]);
  };

  // Submit multiple choice answer
  const handleMultipleChoiceSubmit = async () => {
    if (isSubmitting || selectedOptions.length === 0 || task.submitted) return;
    console.log('Submitting multiple choice answers:', selectedOptions);
    await submitAnswer(selectedOptions);
  };

  // Handle audio file upload using the service
  const handleAudioFileUpload = async (audioBlob: Blob): Promise<{object_name: string, folder: string}> => {
    if (!user) throw new Error('User not authenticated');

    try {
      setIsUploading(true);
      setUploadProgress(0);

      // Use the uploadAudioFile service with the default 'recordings' folder
      const fileInfo = await uploadAudioFile(audioBlob, 'recordings');
      console.log('File info returned from uploadAudioFile:', fileInfo);

      // Simulate progress animation
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 95) {
            clearInterval(interval);
            return 100;
          }
          return prev + 5;
        });
      }, 100);

      return {
        object_name: fileInfo.object_name,
        folder: fileInfo.folder
      };
    } catch (error) {
      console.error('Error uploading audio:', error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  // Handle audio recording submission
  const handleAudioSubmit = async () => {
    if (!recordedAudioUrl) {
      setError('No audio recorded');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      // Fetch the audio blob from the URL
      const response = await fetch(recordedAudioUrl);
      const audioBlob = await response.blob();

      // Upload the audio file using the service
      const fileInfo = await handleAudioFileUpload(audioBlob);
      console.log('Audio uploaded successfully:', fileInfo);

      // For audio answers, we only need to pass the object_name as the answer
      console.log('Audio file uploaded successfully, submitting object_name as answer:', fileInfo.object_name);
      await submitAnswer(fileInfo.object_name);

    } catch (err) {
      console.error('Error submitting audio:', err);
      setError('Failed to submit audio recording');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generic submit answer function
  const submitAnswer = async (answer: any) => {
    // Get the task ID from either id or _id field
    const taskId = task.id || task._id;

    if (!user || !taskId) {
      console.error('Cannot submit answer: user or taskId is missing', {
        user: !!user,
        taskId,
        task_id: task.id,
        task_id_underscore: task._id
      });
      return;
    }

    try {
      console.log(`Submitting answer for task ${taskId} (${task.type}):`, answer);
      setIsSubmitting(true);
      setError(null);

      // For audio tasks, we need to include the folder in the request
      let finalAnswer = answer;

      // If this is a speak_word task and the answer is a string (object_name),
      // we need to include the folder information
      if (task.type === 'speak_word' && typeof answer === 'string') {
        finalAnswer = {
          object_name: answer,
          folder: 'recordings'  // Default folder for audio recordings
        };
        console.log('Submitting audio answer with folder:', finalAnswer);
      }

      const result = await submitTaskAnswer(
        taskId,
        finalAnswer,
        user,
        task.type
      );

      console.log('Submission result:', result);

      // Extract the actual data from the API response
      // The API returns: { success: true, data: { is_correct: true, feedback: "...", ... }, error: null }
      // We need to extract the data field for the component to work correctly
      const submissionData = result.data || result; // Fallback to result if no data field
      console.log('Extracted submission data:', submissionData);
      setSubmissionResult(submissionData);

      // Refresh task score after submission
      fetchTaskScore();

      // Notify parent component
      if (onAnswerSubmitted) {
        onAnswerSubmitted(taskId, submissionData.is_correct || false);
      }

      // Move to next task after a delay
      if (onNext) {
        setTimeout(() => {
          onNext();
        }, 1500);
      }

    } catch (err) {
      console.error('Error submitting answer:', err);
      setError('Failed to submit answer');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render navigation and submit buttons in one row
  const renderNavigationButtons = (submitButton: React.ReactNode) => {
    const { theme } = useTheme();
    const isDarkMode = theme === 'dark';

    return (
      <div className="mt-3 flex justify-between items-center">
        {/* Previous Button */}
        {showPrevious && onPrevious ? (
          <Button
            variant={isDarkMode ? "outline" : "secondary"}
            size="sm"
            onClick={onPrevious}
            disabled={isFirstTask || isLoadingTasks}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 ${
              isFirstTask ? "opacity-50 cursor-not-allowed" : "hover:bg-gray-100 dark:hover:bg-gray-700"
            }`}
          >
            <ArrowLeft className="h-4 w-4" />
            {isLoadingTasks ? "Loading..." : "Previous"}
          </Button>
        ) : (
          <div></div> // Empty div to maintain spacing
        )}

        {/* Submit Button */}
        <div className="flex-shrink-0">
          {submitButton}
        </div>

        {/* Next Button */}
        {showNext && onNext ? (
          <Button
            variant="default"
            size="sm"
            onClick={onNext}
            disabled={isLastTask || isLoadingTasks}
            className={`px-4 py-2 rounded-lg flex items-center gap-2 text-sm font-medium bg-purple-600 hover:bg-purple-700 text-white ${
              isLastTask ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {isLoadingTasks ? "Loading..." : "Next"}
            {!isLoadingTasks && <ArrowRight className="h-4 w-4" />}
            {isLoadingTasks && <Loader2 className="h-4 w-4 animate-spin" />}
          </Button>
        ) : (
          <div></div> // Empty div to maintain spacing
        )}
      </div>
    );
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: { duration: 0.3 }
    }
  };

  const resultVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15
      }
    }
  };

  // Render based on task type
  const renderTaskContent = () => {
    switch (task.type) {
      case 'single_choice':
      case 'multiple_choice':
        return renderChoiceTask();
      case 'image_identification':
        return renderImageIdentificationTask();
      case 'speak_word':
        return renderSpeakWordTask();
      case 'answer_in_word':
        return renderAnswerInWordTask();
      default:
        return (
          <div className="p-4 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
            <p className="dark:text-yellow-200">Unknown task type: {task.type}</p>
          </div>
        );
    }
  };

  // Render answer in word task (text input for Devanagari)
  const renderAnswerInWordTask = () => {
    const questionText = getQuestionText() || 'Type your answer:';

    return (
      <motion.div
        className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-lg font-bold dark:text-white">{questionText}</h3>
          <div className="flex items-center gap-1">
            {showScore && (
              <div className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs font-medium flex items-center mr-1">
                {loadingScore ? (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                    <span className="dark:text-gray-200">Loading...</span>
                  </>
                ) : (
                  <>
                    <span className={`inline-block w-1.5 h-1.5 rounded-full mr-1 ${
                      currentTaskScore.scored > 0 ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-500'
                    }`}></span>
                    <span className="dark:text-gray-200">
                      {currentTaskScore.scored}/{currentTaskScore.total}
                    </span>
                  </>
                )}
              </div>
            )}
            {hasTranslation() && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTranslation(!showTranslation)}
                className="flex items-center gap-1 text-xs px-2 py-1 h-6"
              >
                <Languages className="h-3 w-3" />
                {showTranslation ? 'Original' : 'Translate'}
              </Button>
            )}
            <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full">
              Answer in Word
            </span>
          </div>
        </div>

        {task.audio_hint_url && (
          <div className="mb-3">
            <h4 className="text-sm font-medium mb-1 dark:text-gray-200">Listen to the audio:</h4>
            <audio controls className="w-full h-8">
              <source src={task.audio_hint_url} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        )}

        <div className="mt-3">
          <div className="mb-1 text-xs text-gray-600 dark:text-gray-300">
            <p>Hint: Type in Devanagari script (नेपाली)</p>
            <p className="text-xs opacity-75">You can use Google Input Tools or your system's Nepali keyboard</p>
          </div>
          <input
            type="text"
            className="w-full p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm"
            placeholder="Type your answer here..."
            value={textAnswer}
            onChange={(e) => setTextAnswer(e.target.value)}
            disabled={isSubmitting}
          />
        </div>

        <div className="mt-3 flex justify-center">
          <Button
            size="sm"
            className="bg-purple-600 hover:bg-purple-700 text-white disabled:bg-purple-300 dark:disabled:bg-purple-800"
            onClick={async () => {
              if (isSubmitting || !textAnswer.trim()) return;
              await submitAnswer(textAnswer.trim());
            }}
            disabled={isSubmitting || !textAnswer.trim()}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                Submitting...
              </span>
            ) : 'Submit Answer'}
          </Button>
        </div>

        <AnimatePresence>
          {error && (
            <motion.div
              className="mt-2 p-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-sm"
              variants={resultVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {error}
            </motion.div>
          )}

          {submissionResult && (
            <motion.div
              className={`mt-2 p-2 rounded flex items-center text-sm ${
                submissionResult.is_correct
                  ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                  : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
              }`}
              variants={resultVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {submissionResult.is_correct ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span>Correct!</span>
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  <span>Incorrect. {submissionResult.feedback || ''}</span>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  // Render choice task (single or multiple)
  const renderChoiceTask = () => {
    const isMultiple = task.type === 'multiple_choice';
    const questionText = getQuestionText();
    const optionsWithKeys = getQuestionOptionsWithKeys();

    return (
      <motion.div
        className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">{questionText}</h3>
          <div className="flex items-center gap-2">
            {showScore && (
              <div className="bg-gray-100 dark:bg-gray-700 px-3 py-1.5 rounded text-sm font-medium flex items-center mr-2">
                {loadingScore ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-1" />
                    <span className="text-gray-700 dark:text-gray-200">Loading...</span>
                  </>
                ) : (
                  <>
                    <span className={`inline-block w-2 h-2 rounded-full mr-1.5 ${
                      currentTaskScore.scored > 0 ? 'bg-green-500' : 'bg-gray-400 dark:bg-gray-500'
                    }`}></span>
                    <span className="text-gray-700 dark:text-gray-200 font-semibold">
                      {currentTaskScore.scored}/{currentTaskScore.total}
                    </span>
                  </>
                )}
              </div>
            )}
            {hasTranslation() && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTranslation(!showTranslation)}
                className="flex items-center gap-1 text-sm px-3 py-1.5 h-8 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200"
              >
                <Languages className="h-4 w-4" />
                {showTranslation ? 'Original' : 'Translate'}
              </Button>
            )}
            <span className="text-sm bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100 px-3 py-1.5 rounded-full font-medium">
              {isMultiple ? 'Multiple Choice' : 'Single Choice'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2">
          {optionsWithKeys.map((option, index) => {
            const isSelected = selectedOptions.includes(option.key);
            const isDisabled = isSubmitting || task.submitted;

            return (
              <motion.button
                key={option.key}
                className={`w-full text-left p-3 border rounded-lg transition-colors text-base font-medium ${
                  isSelected
                    ? task.submitted
                      ? task.result === 'correct'
                        ? 'bg-green-100 dark:bg-green-900/70 border-green-500 dark:border-green-400 text-green-800 dark:text-green-200'
                        : 'bg-red-100 dark:bg-red-900/70 border-red-500 dark:border-red-400 text-red-800 dark:text-red-200'
                      : 'bg-purple-100 dark:bg-purple-900/70 border-purple-500 dark:border-purple-400 text-purple-800 dark:text-purple-200'
                    : isDisabled
                      ? 'bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300 dark:border-gray-600'
                      : 'hover:bg-purple-50 dark:hover:bg-purple-900/40 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-500'
                }`}
                onClick={() => isMultiple
                  ? handleMultipleChoiceSelect(option.value)
                  : handleSingleChoiceSelect(option.value)
                }
                disabled={isDisabled}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={!isDisabled ? { scale: 1.01 } : {}}
                whileTap={!isDisabled ? { scale: 0.99 } : {}}
              >
                {option.value}
              </motion.button>
            );
          })}
        </div>

        <AnimatePresence>
          {submissionResult && (
            <motion.div
              className={`mt-2 p-2 rounded flex items-center text-sm ${
                submissionResult.is_correct
                  ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                  : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
              }`}
              variants={resultVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {submissionResult.is_correct ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span>Correct!</span>
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  <span>Incorrect. {submissionResult.feedback || ''}</span>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation and Submit Buttons */}
        {renderNavigationButtons(
          task.submitted ? (
            <Button
              size="sm"
              className="bg-gray-500 hover:bg-gray-500 text-white cursor-not-allowed px-4 py-2 text-sm font-medium"
              disabled={true}
            >
              {task.result === 'correct' ? '✓ Submitted (Correct)' : '✗ Submitted (Incorrect)'}
            </Button>
          ) : (
            <Button
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 text-white disabled:bg-purple-300 dark:disabled:bg-purple-800 px-4 py-2 text-sm font-medium"
              onClick={isMultiple ? () => submitAnswer(selectedOptions) : () => submitAnswer(selectedOptions[0])}
              disabled={isSubmitting || selectedOptions.length === 0}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </span>
              ) : 'Submit Answer'}
            </Button>
          )
        )}
      </motion.div>
    );
  };

  // Helper function to get media URL
  const getMediaUrl = () => {
    // Check question.media_url first (new structure)
    if (task.question && typeof task.question === 'object' && 'media_url' in task.question) {
      const questionMediaUrl = (task.question as any).media_url;
      if (questionMediaUrl) {
        console.log('Using question.media_url:', questionMediaUrl);
        return questionMediaUrl;
      }
    }

    // Fallback to task level media_url (backward compatibility)
    if (task.media_url) {
      console.log('Using task.media_url:', task.media_url);
      return task.media_url;
    }

    console.log('No media URL found');
    return null;
  };

  // Helper function to determine media type
  const getMediaType = (url: string) => {
    if (!url) return 'unknown';

    const extension = url.split('.').pop()?.toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'm4a'];
    const videoExtensions = ['mp4', 'webm', 'avi', 'mov'];

    if (imageExtensions.includes(extension || '')) return 'image';
    if (audioExtensions.includes(extension || '')) return 'audio';
    if (videoExtensions.includes(extension || '')) return 'video';

    // Fallback: check URL patterns or assume image for image_identification tasks
    if (task.type === 'image_identification') return 'image';

    return 'unknown';
  };

  // Render media content based on type
  const renderMediaContent = (mediaUrl: string) => {
    const mediaType = getMediaType(mediaUrl);

    switch (mediaType) {
      case 'image':
        return (
          <motion.div
            className="mb-3"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative group cursor-pointer" onClick={() => setShowImagePreview(true)}>
              <img
                src={mediaUrl}
                alt="Task media content"
                className="max-w-64 h-auto rounded-lg mx-auto border-2 border-gray-200 dark:border-gray-600 shadow-md hover:shadow-lg transition-shadow"
                onError={(e) => {
                  console.error('Failed to load image:', mediaUrl);
                  e.currentTarget.style.display = 'none';
                }}
                onLoad={() => {
                  console.log('Image loaded successfully:', mediaUrl);
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                <Expand className="h-8 w-8 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
              <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-sm px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                Click to expand
              </div>
            </div>
          </motion.div>
        );

      case 'audio':
        return (
          <motion.div
            className="mb-6"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <audio controls className="w-full">
              <source src={mediaUrl} type="audio/mpeg" />
              <source src={mediaUrl} type="audio/wav" />
              Your browser does not support the audio element.
            </audio>
          </motion.div>
        );

      case 'video':
        return (
          <motion.div
            className="mb-6"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <video controls className="max-w-full h-auto rounded-lg mx-auto border border-gray-200 dark:border-gray-700 shadow-sm">
              <source src={mediaUrl} type="video/mp4" />
              <source src={mediaUrl} type="video/webm" />
              Your browser does not support the video element.
            </video>
          </motion.div>
        );

      default:
        return (
          <motion.div
            className="mb-6 p-4 bg-yellow-100 dark:bg-yellow-900 rounded-lg"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
          >
            <p className="text-yellow-800 dark:text-yellow-200">
              Media content available but format not supported for preview.
            </p>
            <a
              href={mediaUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 underline"
            >
              View media content
            </a>
          </motion.div>
        );
    }
  };

  // Render image identification task
  const renderImageIdentificationTask = () => {
    const questionText = getQuestionText();
    const optionsWithKeys = getQuestionOptionsWithKeys();
    const mediaUrl = getMediaUrl();

    // Debug logging
    console.log('=== Image Identification Task Debug ===');
    console.log('Task object:', task);
    console.log('Task type:', task.type);
    console.log('Task media_url:', task.media_url);
    console.log('Task question:', task.question);
    if (task.question && typeof task.question === 'object') {
      console.log('Question media_url:', (task.question as any).media_url);
    }
    console.log('Selected mediaUrl:', mediaUrl);
    console.log('Question text:', questionText);
    console.log('Options:', optionsWithKeys);

    return (
      <motion.div
        className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">{questionText}</h3>
          <div className="flex items-center gap-2">
            {showScore && (
              <div className="bg-gray-100 dark:bg-gray-700 px-3 py-1.5 rounded text-sm font-medium flex items-center mr-2">
                {loadingScore ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-1" />
                    <span className="text-gray-700 dark:text-gray-200">Loading...</span>
                  </>
                ) : (
                  <>
                    <span className={`inline-block w-2 h-2 rounded-full mr-1.5 ${
                      currentTaskScore.scored > 0 ? 'bg-green-500' : 'bg-gray-400 dark:bg-gray-500'
                    }`}></span>
                    <span className="text-gray-700 dark:text-gray-200 font-semibold">
                      {currentTaskScore.scored}/{currentTaskScore.total}
                    </span>
                  </>
                )}
              </div>
            )}
            {hasTranslation() && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTranslation(!showTranslation)}
                className="flex items-center gap-1 text-sm px-3 py-1.5 h-8 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200"
              >
                <Languages className="h-4 w-4" />
                {showTranslation ? 'Original' : 'Translate'}
              </Button>
            )}
            <span className="text-sm bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-100 px-3 py-1.5 rounded-full font-medium">
              Image Identification
            </span>
          </div>
        </div>

        {mediaUrl && renderMediaContent(mediaUrl)}

        <div className="grid grid-cols-2 gap-2">
          {optionsWithKeys.map((option, index) => {
            const isSelected = selectedOptions.includes(option.key);
            const isDisabled = isSubmitting || task.submitted;

            return (
              <motion.button
                key={option.key}
                className={`w-full text-left p-3 border rounded-lg transition-colors text-base font-medium ${
                  isSelected
                    ? task.submitted
                      ? task.result === 'correct'
                        ? 'bg-green-100 dark:bg-green-900/70 border-green-500 dark:border-green-400 text-green-800 dark:text-green-200'
                        : 'bg-red-100 dark:bg-red-900/70 border-red-500 dark:border-red-400 text-red-800 dark:text-red-200'
                      : 'bg-purple-100 dark:bg-purple-900/70 border-purple-500 dark:border-purple-400 text-purple-800 dark:text-purple-200'
                    : isDisabled
                      ? 'bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300 dark:border-gray-600'
                      : 'hover:bg-purple-50 dark:hover:bg-purple-900/40 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-500'
                }`}
                onClick={() => handleSingleChoiceSelect(option.value)}
                disabled={isDisabled}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={!isDisabled ? { scale: 1.01 } : {}}
                whileTap={!isDisabled ? { scale: 0.99 } : {}}
              >
                {option.value}
              </motion.button>
            );
          })}
        </div>

        <AnimatePresence>
          {submissionResult && (
            <motion.div
              className={`mt-2 p-2 rounded flex items-center text-sm ${
                submissionResult.is_correct
                  ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                  : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
              }`}
              variants={resultVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {submissionResult.is_correct ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span>Correct!</span>
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  <span>Incorrect. {submissionResult.feedback || ''}</span>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation and Submit Buttons */}
        {renderNavigationButtons(
          task.submitted ? (
            <Button
              size="sm"
              className="bg-gray-500 hover:bg-gray-500 text-white cursor-not-allowed px-4 py-2 text-sm font-medium"
              disabled={true}
            >
              {task.result === 'correct' ? '✓ Submitted (Correct)' : '✗ Submitted (Incorrect)'}
            </Button>
          ) : (
            <Button
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 text-white disabled:bg-purple-300 dark:disabled:bg-purple-800 px-4 py-2 text-sm font-medium"
              onClick={() => submitAnswer(selectedOptions[0])}
              disabled={isSubmitting || selectedOptions.length === 0}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </span>
              ) : 'Submit Answer'}
            </Button>
          )
        )}
      </motion.div>
    );
  };

  // Render speak word task
  const renderSpeakWordTask = () => {
    return (
      <motion.div
        className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-lg font-bold dark:text-white">Speak this word:</h3>
          <div className="flex items-center gap-1">
            {showScore && (
              <div className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs font-medium flex items-center mr-1">
                {loadingScore ? (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin mr-1" />
                    <span className="dark:text-gray-200">Loading...</span>
                  </>
                ) : (
                  <>
                    <span className={`inline-block w-1.5 h-1.5 rounded-full mr-1 ${
                      currentTaskScore.scored > 0 ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-500'
                    }`}></span>
                    <span className="dark:text-gray-200">
                      {currentTaskScore.scored}/{currentTaskScore.total}
                    </span>
                  </>
                )}
              </div>
            )}
            <span className="text-xs bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 px-2 py-1 rounded-full">
              Speak Word
            </span>
          </div>
        </div>
        <motion.div
          className="text-2xl font-bold text-center p-4 mb-3 bg-purple-50 dark:bg-purple-900/30 rounded-lg dark:text-white"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 300, damping: 10 }}
        >
          {task.word}
        </motion.div>

        {task.audio_hint_url && (
          <div className="mb-3">
            <h4 className="text-sm font-medium mb-1 dark:text-gray-200">Listen to pronunciation:</h4>
            <audio controls className="w-full h-8">
              <source src={task.audio_hint_url} type="audio/mpeg" />
              Your browser does not support the audio element.
            </audio>
          </div>
        )}

        <div className="mt-4">
          <AnimatePresence mode="wait">
            {!isRecording && !recordedAudioUrl && (
              <motion.div
                key="start-recording"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <Button
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700 text-white w-full flex items-center justify-center gap-2"
                  onClick={startRecording}
                  disabled={isSubmitting}
                >
                  <Mic className="h-4 w-4" />
                  Start Recording
                </Button>
              </motion.div>
            )}

            {isRecording && (
              <motion.div
                key="stop-recording"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <Button
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white w-full flex items-center justify-center gap-2"
                  onClick={stopRecording}
                >
                  <MicOff className="h-4 w-4" />
                  Stop Recording
                </Button>

                <motion.div
                  className="mt-2 p-2 bg-red-50 dark:bg-red-900/30 rounded flex items-center justify-center"
                  initial={{ scale: 0.9 }}
                  animate={{ scale: 1 }}
                  transition={{ repeat: Infinity, repeatType: "reverse", duration: 1 }}
                >
                  <span className="text-red-600 dark:text-red-400 text-xs">Recording in progress...</span>
                </motion.div>
              </motion.div>
            )}

            {recordedAudioUrl && (
              <motion.div
                className="space-y-3"
                key="audio-controls"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <audio ref={audioRef} controls className="w-full h-8">
                  <source src={recordedAudioUrl} type="audio/webm" />
                  Your browser does not support the audio element.
                </audio>

                <div className="flex space-x-2 justify-center">
                  <Button
                    size="sm"
                    className="bg-purple-600 hover:bg-purple-700 text-white flex-1 flex items-center justify-center gap-2"
                    onClick={handleAudioSubmit}
                    disabled={isSubmitting || isUploading}
                  >
                    {isSubmitting || isUploading ? (
                      <>
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-3 w-3" />
                        Submit Recording
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center justify-center gap-2"
                    onClick={() => {
                      startRecording();
                    }}
                    disabled={isSubmitting || isUploading}
                  >
                    <RefreshCw className="h-3 w-3" />
                    Record Again
                  </Button>
                </div>

                {isUploading && (
                  <div className="w-full space-y-1">
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>Uploading...</span>
                      <span>{uploadProgress}%</span>
                    </div>
                    <Progress value={uploadProgress} className="h-1" />
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <AnimatePresence>
          {error && (
            <motion.div
              className="mt-2 p-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-sm"
              variants={resultVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {error}
            </motion.div>
          )}

          {submissionResult && (
            <motion.div
              className={`mt-2 p-2 rounded flex items-center text-sm ${
                submissionResult.is_correct
                  ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                  : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
              }`}
              variants={resultVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              {submissionResult.is_correct ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  <span>Correct pronunciation!</span>
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  <span>Needs improvement. {submissionResult.feedback || ''}</span>
                </>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  const currentTaskScore = getTaskScore();
  const showScore = currentTaskScore.scored !== undefined || currentTaskScore.total !== undefined || score !== undefined;

  return (
    <div className="task-item">

      {renderTaskContent()}

      {/* Full-screen image preview modal */}
      <AnimatePresence>
        {showImagePreview && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowImagePreview(false)}
          >
            <motion.div
              className="relative max-w-full max-h-full"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.8 }}
              onClick={(e) => e.stopPropagation()}
            >
              <img
                src={task.question && typeof task.question === 'object' && (task.question as any).media_url
                  ? (task.question as any).media_url
                  : task.media_url}
                alt="Task media content - Full view"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
              <button
                className="absolute top-4 right-4 bg-black bg-opacity-60 text-white p-2 rounded-full hover:bg-opacity-80 transition-all"
                onClick={() => setShowImagePreview(false)}
              >
                <X className="h-6 w-6" />
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TaskItem;
