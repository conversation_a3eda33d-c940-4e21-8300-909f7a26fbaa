import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface TaskPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

/**
 * Enhanced component for task list pagination with animations and theme support
 */
const TaskPagination: React.FC<TaskPaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
  onPageSizeChange,
}) => {
  const { isDarkMode } = useTheme();

  // Calculate start and end item numbers
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // Show all pages if there are few
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show a subset of pages with current page in the middle
      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
      let endPage = startPage + maxPagesToShow - 1;

      if (endPage > totalPages) {
        endPage = totalPages;
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex justify-between items-center"
    >
      {/* Page info - left side */}
      <div className={cn(
        "text-xs",
        isDarkMode ? "text-gray-300" : "text-gray-500"
      )}>
        <div className="flex items-center gap-2">
          <span className={isDarkMode ? "text-gray-300" : "text-gray-600"}>Show</span>
          <Select
            value={pageSize.toString()}
            onValueChange={(value) => onPageSizeChange(parseInt(value))}
          >
            <SelectTrigger className={cn(
              "w-[60px] h-7 text-xs",
              isDarkMode ? "border-gray-600 bg-gray-800 text-gray-200" : ""
            )}>
              <SelectValue />
            </SelectTrigger>
            <SelectContent className={isDarkMode ? "bg-gray-800 text-gray-200 border-gray-700" : "text-gray-700"}>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
          <span className={isDarkMode ? "text-gray-300" : "text-gray-600"}>· {startItem}-{endItem} of {totalItems}</span>
        </div>
      </div>

      {/* Pagination controls - right side */}
      <div className="flex items-center space-x-1">
        {/* Previous page */}
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={cn(
            "h-6 w-6",
            isDarkMode ? "border-gray-600 text-gray-200 hover:bg-gray-700" : ""
          )}
        >
          <ChevronLeft className={cn(
            "h-3 w-3",
            isDarkMode ? "text-gray-300" : "text-gray-600"
          )} />
        </Button>

        {/* Current page indicator */}
        <span className={cn(
          "text-xs px-2 font-medium",
          isDarkMode ? "text-gray-200" : "text-gray-700"
        )}>
          {currentPage} / {totalPages}
        </span>

        {/* Next page */}
        <Button
          variant="outline"
          size="icon"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages || totalPages === 0}
          className={cn(
            "h-6 w-6",
            isDarkMode ? "border-gray-600 text-gray-200 hover:bg-gray-700" : ""
          )}
        >
          <ChevronRight className={cn(
            "h-3 w-3",
            isDarkMode ? "text-gray-300" : "text-gray-600"
          )} />
        </Button>
      </div>
    </motion.div>
  );
};

export default TaskPagination;
