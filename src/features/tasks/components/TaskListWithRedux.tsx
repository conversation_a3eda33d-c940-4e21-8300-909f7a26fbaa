import React, { useEffect, useState } from 'react';
import { useReduxApi } from '@/hooks/useReduxApi';
import { taskListService, TaskSetFilter, TaskSetResponse } from '@/api/taskListService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, AlertCircle } from 'lucide-react';
import TaskFilter from './TaskFilter';
import { motion, AnimatePresence } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { useTheme } from '@/core/hooks/useTheme';

/**
 * Enhanced task list component using Redux for API state management
 * with animations and theme support
 */
const TaskListWithRedux: React.FC = () => {
  // Theme hook
  const { theme } = useTheme();
  
  // Default filter state
  const [filter, setFilter] = useState<TaskSetFilter>({
    page: 1,
    limit: 10,
    sort_by: 'created_at',
    sort_order: -1
  });

  // Redux API hooks
  const { createApiCallbacks, startLoading, isLoading, getError, getData } = useReduxApi();
  
  // API key for this component
  const API_KEY = 'taskList';
  
  // Get data from Redux store
  const taskListData = getData<TaskSetResponse>(API_KEY);
  const loading = isLoading(API_KEY);
  const error = getError(API_KEY);

  // Fetch task sets on mount and when filter changes
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Start loading
        startLoading(API_KEY);
        
        // Create callbacks for Redux integration
        const callbacks = createApiCallbacks<TaskSetResponse>(API_KEY);
        
        // Fetch task sets
        await taskListService.fetchTaskSets(filter, callbacks);
      } catch (err) {
        console.error('Error fetching task sets:', err);
      }
    };

    fetchData();
  }, [filter]);

  // Handle filter changes
  const handleFilterChange = (newFilter: TaskSetFilter) => {
    setFilter(newFilter);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilter(prev => ({ ...prev, page }));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { type: "spring", stiffness: 100, damping: 15 }
    }
  };

  return (
    <motion.div 
      className="space-y-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex justify-between items-center">
        <motion.h1 
          className="text-2xl font-bold dark:text-white"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          Task List
        </motion.h1>
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Button
            variant="outline"
            onClick={() => {
              startLoading(API_KEY);
              const callbacks = createApiCallbacks<TaskSetResponse>(API_KEY);
              taskListService.fetchTaskSets(filter, callbacks);
            }}
            disabled={loading}
            className="flex items-center gap-2"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </motion.div>
      </div>
      
      {/* Filter component */}
      <TaskFilter filter={filter} onFilterChange={handleFilterChange} />
      
      {/* Loading state */}
      <AnimatePresence>
        {loading && (
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="overflow-hidden dark:bg-gray-800 dark:border-gray-700">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-3/4 dark:bg-gray-700" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full dark:bg-gray-700" />
                    <Skeleton className="h-4 w-2/3 dark:bg-gray-700" />
                    <Skeleton className="h-4 w-1/2 dark:bg-gray-700" />
                    <div className="pt-2">
                      <Skeleton className="h-8 w-24 dark:bg-gray-700" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Error state */}
      <AnimatePresence>
        {error && !loading && (
          <motion.div 
            className="bg-red-50 dark:bg-red-900/30 p-4 rounded-md text-red-800 dark:text-red-200"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ type: "spring", stiffness: 100, damping: 15 }}
          >
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              <p>Error: {error}</p>
            </div>
            <Button 
              variant="outline" 
              className="mt-2"
              onClick={() => {
                startLoading(API_KEY);
                const callbacks = createApiCallbacks<TaskSetResponse>(API_KEY);
                taskListService.fetchTaskSets(filter, callbacks);
              }}
            >
              Retry
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Task list */}
      <AnimatePresence>
        {!loading && !error && taskListData && (
          <motion.div
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            <motion.div 
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
              variants={containerVariants}
            >
              {taskListData.items.map((task, index) => (
                <motion.div key={task._id} variants={itemVariants}>
                  <Card className="hover:shadow-md transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg dark:text-white">{task.input_content || 'Task Set'}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        <p>Status: <span className="font-medium">{task.status}</span></p>
                        <p>Created: <span className="font-medium">{new Date(task.created_at).toLocaleDateString()}</span></p>
                        <p>Tasks: <span className="font-medium">{task.tasks.length}</span></p>
                      </div>
                      <div className="mt-4">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => window.location.href = `/tasks/${task._id}`}
                          className="hover:bg-purple-50 hover:text-purple-700 dark:hover:bg-purple-900/30 dark:hover:text-purple-300"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          View Details
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>
            
            {/* Pagination */}
            {taskListData.total > 0 && (
              <motion.div 
                className="flex justify-center mt-6"
                variants={itemVariants}
              >
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={taskListData.page <= 1}
                    onClick={() => handlePageChange(taskListData.page - 1)}
                  >
                    Previous
                  </Button>
                  
                  <div className="flex items-center px-4 text-sm dark:text-gray-300">
                    Page {taskListData.page} of {taskListData.pages}
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={taskListData.page >= taskListData.pages}
                    onClick={() => handlePageChange(taskListData.page + 1)}
                  >
                    Next
                  </Button>
                </div>
              </motion.div>
            )}
            
            {/* Empty state */}
            {taskListData.items.length === 0 && (
              <motion.div 
                className="text-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg"
                variants={itemVariants}
              >
                <p className="text-gray-500 dark:text-gray-400">No tasks found</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">Try adjusting your filters or creating new tasks</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => {
                    startLoading(API_KEY);
                    const callbacks = createApiCallbacks<TaskSetResponse>(API_KEY);
                    taskListService.fetchTaskSets(filter, callbacks);
                  }}
                >
                  Refresh
                </Button>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default TaskListWithRedux;
