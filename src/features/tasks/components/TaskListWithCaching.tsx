import React, { useEffect, useState, useRef } from 'react';
import { useReduxApi } from '@/hooks/useReduxApi';
import { useRequestCancellation } from '@/hooks/useRequestCancellation';
import { taskListService, TaskSetFilter, TaskSetResponse, ApiTaskSetResponse } from '@/api/taskListService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, AlertCircle } from 'lucide-react';
import TaskFilter from './TaskFilter';
import ErrorBoundary from '@/components/ErrorBoundary';
import useErrorHandler from '@/hooks/useErrorHandler';
import { ApiError } from '@/api/httpBase';
import { motion, AnimatePresence } from 'framer-motion';
import { Skeleton } from '@/components/ui/skeleton';
import { useTheme } from '@/core/hooks/useTheme';

/**
 * Enhanced task list component using Redux for API state management
 * with request cancellation, caching, animations and theme support
 */
const TaskListWithCaching: React.FC = () => {
  // Theme hook
  const { theme } = useTheme();

  // Default filter state
  const [filter, setFilter] = useState<TaskSetFilter>({
    page: 1,
    limit: 10,
    sort_by: 'created_at',
    sort_order: -1
  });

  // Redux API hooks
  const { createApiCallbacks, startLoading, isLoading, getError, getData } = useReduxApi();

  // Request cancellation hook
  const { generateRequestId, cancelAllRequests } = useRequestCancellation();

  // Error handler hook
  const { handleError } = useErrorHandler();

  // API key for this component
  const API_KEY = 'taskList';

  // Flag to track if a fetch has failed
  const hasFetchFailedRef = useRef<boolean>(false);

  // Get data from Redux store
  const taskListData = getData<TaskSetResponse>(API_KEY);
  const loading = isLoading(API_KEY);
  const error = getError(API_KEY);

  // Fetch task sets on mount and when filter changes
  useEffect(() => {
    // Static flag to track if we're already fetching
    let isFetching = false;

    // Reset fetch failed flag when filter changes
    hasFetchFailedRef.current = false;

    const fetchData = async () => {
      // If we're already fetching or a previous fetch has failed, don't try again
      if (isFetching || hasFetchFailedRef.current) {
        console.log('Skipping fetch: ' +
          (isFetching ? 'Already fetching data.' : 'Previous fetch attempt failed.'));
        return;
      }

      try {
        // Set fetching flag to prevent duplicate requests
        isFetching = true;

        // Start loading
        startLoading(API_KEY);

        // Generate a unique request ID
        const requestId = generateRequestId('taskList');

        // Create callbacks for Redux integration with custom error handling
        const callbacks = {
          ...createApiCallbacks<ApiTaskSetResponse>(API_KEY),
          onError: (error: ApiError) => {
            // Set fetch failed flag for any error
            console.error('API error:', error);
            hasFetchFailedRef.current = true;
            isFetching = false;

            // Call the original onError callback
            const originalCallbacks = createApiCallbacks<ApiTaskSetResponse>(API_KEY);
            if (originalCallbacks.onError) {
              originalCallbacks.onError(error);
            }
          },
          onFinally: () => {
            // Reset fetching flag when request completes
            isFetching = false;

            // Call original onFinally if it exists
            const originalCallbacks = createApiCallbacks<ApiTaskSetResponse>(API_KEY);
            if (originalCallbacks.onFinally) {
              originalCallbacks.onFinally();
            }
          }
        };

        // Create cache key based on filter
        const cacheKey = `taskList:${JSON.stringify(filter)}`;

        // Fetch task sets with caching and request cancellation
        await taskListService.fetchTaskSets(
          filter,
          callbacks,
          {
            cache: {
              key: cacheKey,
              ttl: 5 * 60 * 1000, // 5 minutes
              bypass: false
            },
            requestId
          }
        );

        // Reset fetch failed flag on success
        hasFetchFailedRef.current = false;
        isFetching = false;
      } catch (err) {
        console.error('Error fetching task sets:', err);
        handleError(err);

        // Set fetch failed flag to prevent continuous retries
        hasFetchFailedRef.current = true;
        isFetching = false;

        // Log that we're preventing further automatic retries
        console.log('Automatic retries disabled until user refreshes or changes filter');
      }
    };

    fetchData();

    // Cancel all requests on cleanup
    return () => {
      cancelAllRequests();
    };
  }, [filter]);

  // Handle filter changes
  const handleFilterChange = (newFilter: TaskSetFilter) => {
    setFilter(newFilter);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilter(prev => ({ ...prev, page }));
  };

  // Handle refresh
  const handleRefresh = () => {
    // Reset fetch failed flag
    hasFetchFailedRef.current = false;

    // Create cache key based on filter
    const cacheKey = `taskList:${JSON.stringify(filter)}`;

    // Clear cache for this key
    taskListService.clearCache(cacheKey);

    // Cancel any pending requests
    cancelAllRequests();

    // Refetch data by triggering a filter change
    setFilter(prev => ({
      ...prev,
      // Add a timestamp to force a new request even if filter values are the same
      _timestamp: Date.now()
    }));
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 100, damping: 15 }
    }
  };

  return (
    <ErrorBoundary>
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex justify-between items-center">
          <motion.h1
            className="text-2xl font-bold dark:text-white"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            Task List
          </motion.h1>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </Button>
          </motion.div>
        </div>

        {/* Filter component */}
        <TaskFilter filter={filter} onFilterChange={handleFilterChange} />

        {/* Loading state */}
        <AnimatePresence>
          {loading && (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <Skeleton className="h-6 w-3/4" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                      <Skeleton className="h-4 w-1/2" />
                      <div className="pt-2">
                        <Skeleton className="h-8 w-24" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Error state */}
        <AnimatePresence>
          {error && !loading && (
            <motion.div
              className="bg-red-50 dark:bg-red-900/30 p-4 rounded-md text-red-800 dark:text-red-200"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ type: "spring", stiffness: 100, damping: 15 }}
            >
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                <p>Error: {error}</p>
              </div>
              <Button
                variant="outline"
                className="mt-2"
                onClick={handleRefresh}
              >
                Retry
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Task list */}
        <AnimatePresence>
          {!loading && !error && taskListData && (
            <motion.div
              initial="hidden"
              animate="visible"
              variants={containerVariants}
            >
              {/* Debug info - only visible during development */}
              {import.meta.env.DEV && (
                <motion.div
                  className="mb-4 p-4 bg-gray-100 dark:bg-gray-800 rounded-md text-xs overflow-auto max-h-40"
                  variants={itemVariants}
                >
                  <details>
                    <summary className="font-bold cursor-pointer dark:text-gray-200">Debug Info</summary>
                    <pre className="mt-2 dark:text-gray-300">
                      {JSON.stringify({
                        taskListData: {
                          itemsCount: taskListData.items?.length || 0,
                          total: taskListData.total || (taskListData.pagination?.total_count || 0),
                          page: taskListData.page || (taskListData.pagination?.page || 1),
                          limit: taskListData.limit || (taskListData.pagination?.limit || 10),
                          pages: taskListData.pages || (taskListData.pagination?.total_pages || 1),
                          pagination: taskListData.pagination
                        },
                        firstItem: taskListData.items?.length > 0 ? {
                          id: taskListData.items[0].id,
                          _id: taskListData.items[0]._id,
                          input_content: taskListData.items[0].input_content
                        } : null
                      }, null, 2)}
                    </pre>
                  </details>
                </motion.div>
              )}

              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                variants={containerVariants}
              >
                {taskListData.items && taskListData.items.map((task, index) => (
                  <motion.div key={task.id || task._id} variants={itemVariants}>
                    <Card className="hover:shadow-md transition-shadow dark:bg-gray-800 dark:border-gray-700">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg dark:text-white">{task.input_content || 'Task Set'}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          <p>Status: <span className="font-medium">{task.status}</span></p>
                          <p>Created: <span className="font-medium">{new Date(task.created_at).toLocaleDateString()}</span></p>
                          <p>Tasks: <span className="font-medium">{task.tasks.length}</span></p>
                        </div>
                        <div className="mt-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.location.href = `/tasks/${task.id || task._id}`}
                            className="hover:bg-purple-50 hover:text-purple-700 dark:hover:bg-purple-900/30 dark:hover:text-purple-300"
                          >
                            View Details
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>

              {/* Pagination */}
              {taskListData.total > 0 && (
                <motion.div
                  className="flex justify-center mt-6"
                  variants={itemVariants}
                >
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={taskListData.page <= 1}
                      onClick={() => handlePageChange(taskListData.page - 1)}
                    >
                      Previous
                    </Button>

                    <div className="flex items-center px-4 text-sm dark:text-gray-300">
                      Page {taskListData.page} of {taskListData.pages}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      disabled={taskListData.page >= taskListData.pages}
                      onClick={() => handlePageChange(taskListData.page + 1)}
                    >
                      Next
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Empty state */}
              {taskListData.items.length === 0 && (
                <motion.div
                  className="text-center p-8 bg-gray-50 dark:bg-gray-800 rounded-lg"
                  variants={itemVariants}
                >
                  <p className="text-gray-500 dark:text-gray-400">No tasks found</p>
                  <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">Try adjusting your filters or creating new tasks</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={handleRefresh}
                  >
                    Refresh
                  </Button>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </ErrorBoundary>
  );
};

export default TaskListWithCaching;
