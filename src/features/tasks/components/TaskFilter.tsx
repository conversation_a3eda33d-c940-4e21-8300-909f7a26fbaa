import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Search, X, Filter, SlidersHorizontal } from 'lucide-react';
import { format } from 'date-fns';
import { TaskSetFilter } from '@/api/taskListService';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface TaskFilterProps {
  filter: TaskSetFilter;
  onFilterChange: (filter: TaskSetFilter) => void;
}

// Task status options
const STATUS_OPTIONS = [
  { value: 'all', label: 'All Statuses' },
  { value: 'completed', label: 'Completed' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'pending', label: 'Pending' }
];

// Sort options
const SORT_OPTIONS = [
  { value: 'created_at:-1', label: 'Date (Newest First) ↓' },
  { value: 'created_at:1', label: 'Date (Oldest First) ↑' },
  { value: 'input_content:1', label: 'Content (A-Z) ↑' },
  { value: 'input_content:-1', label: 'Content (Z-A) ↓' },
  { value: 'status:1', label: 'Status (A-Z) ↑' },
  { value: 'status:-1', label: 'Status (Z-A) ↓' },
  { value: 'max_score:-1', label: 'Score (High-Low) ↓' },
  { value: 'max_score:1', label: 'Score (Low-High) ↑' }
];

/**
 * Enhanced component for filtering task sets with animations and theme support
 */
const TaskFilter: React.FC<TaskFilterProps> = ({ filter, onFilterChange }) => {
  // Theme
  const { isDarkMode } = useTheme();

  // Collapsible state for advanced filters
  const [isOpen, setIsOpen] = useState(false);

  // Local state for form inputs
  const [search, setSearch] = useState(filter.search || '');
  const [status, setStatus] = useState(filter.status || 'all');
  const [dateFrom, setDateFrom] = useState<Date | undefined>(
    filter.start_date ? new Date(filter.start_date) : undefined
  );
  const [dateTo, setDateTo] = useState<Date | undefined>(
    filter.end_date ? new Date(filter.end_date) : undefined
  );
  const [sortBy, setSortBy] = useState(filter.sort_by || 'created_at');
  // Ensure sort order is stored as a string but represents a number (1 or -1)
  const [sortOrder, setSortOrder] = useState(
    filter.sort_order !== undefined ? filter.sort_order.toString() : '-1'
  );

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (search) count++;
    if (status !== 'all') count++;
    if (dateFrom) count++;
    if (dateTo) count++;
    if (sortBy !== 'created_at' || sortOrder !== '-1') count++;
    return count;
  };

  // Apply filters
  const applyFilters = () => {
    // Parse sort order to number (1 for ascending, -1 for descending)
    const numericSortOrder = parseInt(sortOrder);

    console.log('Applying filters with sort:', sortBy, 'order:', numericSortOrder);

    onFilterChange({
      ...filter,
      search,
      status: status === 'all' ? undefined : status,
      start_date: dateFrom ? format(dateFrom, 'yyyy-MM-dd') : undefined,
      end_date: dateTo ? format(dateTo, 'yyyy-MM-dd') : undefined,
      sort_by: sortBy,
      sort_order: numericSortOrder, // Ensure it's a number
      page: 1, // Reset to first page when filters change
    });
  };

  // Reset filters
  const resetFilters = () => {
    // Reset local state
    setSearch('');
    setStatus('all');
    setDateFrom(undefined);
    setDateTo(undefined);
    setSortBy('created_at');
    setSortOrder('-1');

    // Apply reset to filter state with numeric sort order
    onFilterChange({
      page: 1,
      limit: filter.limit,
      sort_by: 'created_at',
      sort_order: -1, // Ensure it's a number (-1 for descending)
      // Clear all other filters
      search: undefined,
      status: undefined,
      start_date: undefined,
      end_date: undefined
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "w-full",
        isDarkMode ? "text-gray-200" : "text-gray-800"
      )}
    >
      <div className="flex items-center gap-2">
        {/* Search input - compact */}
        <div className="relative w-[180px]">
          <Input
            placeholder="Search tasks..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className={cn(
              "pl-8 h-8 text-sm",
              isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
            )}
          />
          <Search className={cn(
            "absolute left-2 top-2 h-4 w-4",
            isDarkMode ? "text-gray-300" : "text-gray-500"
          )} />
        </div>

        {/* Start Date picker */}
        <div className="flex items-center">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "h-8 px-2 text-xs min-w-[100px] justify-start",
                  isDarkMode ? "bg-gray-800 border-gray-700 text-gray-200" : "bg-white text-gray-700"
                )}
              >
                <CalendarIcon className={cn(
                  "h-3.5 w-3.5 mr-1",
                  isDarkMode ? "text-gray-300" : "text-gray-600"
                )} />
                {dateFrom ? format(dateFrom, 'MM/dd/yy') : 'Start Date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="p-2 flex flex-col space-y-2">
                <div className="flex items-center justify-between">
                  <span className={cn(
                    "text-xs font-medium",
                    isDarkMode ? "text-gray-200" : "text-gray-700"
                  )}>Start Date:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-6 text-xs",
                      isDarkMode ? "text-gray-200" : "text-gray-700"
                    )}
                    onClick={() => setDateFrom(undefined)}
                    disabled={!dateFrom}
                  >
                    <X className={cn(
                      "h-3 w-3 mr-1",
                      isDarkMode ? "text-gray-300" : "text-gray-600"
                    )} />Clear
                  </Button>
                </div>
                <Calendar
                  mode="single"
                  selected={dateFrom}
                  onSelect={(date) => {
                    setDateFrom(date);
                    // Auto-apply when date changes
                    setTimeout(() => applyFilters(), 0);
                  }}
                  initialFocus
                  className={cn("rounded-md border", isDarkMode ? "bg-gray-800 text-gray-200 border-gray-700" : "border-gray-200 text-gray-700")}
                />
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* End Date picker */}
        <div className="flex items-center">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className={cn(
                  "h-8 px-2 text-xs min-w-[100px] justify-start",
                  isDarkMode ? "bg-gray-800 border-gray-700 text-gray-200" : "bg-white text-gray-700"
                )}
              >
                <CalendarIcon className={cn(
                  "h-3.5 w-3.5 mr-1",
                  isDarkMode ? "text-gray-300" : "text-gray-600"
                )} />
                {dateTo ? format(dateTo, 'MM/dd/yy') : 'End Date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <div className="p-2 flex flex-col space-y-2">
                <div className="flex items-center justify-between">
                  <span className={cn(
                    "text-xs font-medium",
                    isDarkMode ? "text-gray-200" : "text-gray-700"
                  )}>End Date:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-6 text-xs",
                      isDarkMode ? "text-gray-200" : "text-gray-700"
                    )}
                    onClick={() => setDateTo(undefined)}
                    disabled={!dateTo}
                  >
                    <X className={cn(
                      "h-3 w-3 mr-1",
                      isDarkMode ? "text-gray-300" : "text-gray-600"
                    )} />Clear
                  </Button>
                </div>
                <Calendar
                  mode="single"
                  selected={dateTo}
                  onSelect={(date) => {
                    setDateTo(date);
                    // Auto-apply when date changes
                    setTimeout(() => applyFilters(), 0);
                  }}
                  initialFocus
                  className={cn("rounded-md border", isDarkMode ? "bg-gray-800 text-gray-200 border-gray-700" : "border-gray-200 text-gray-700")}
                />
              </div>
            </PopoverContent>
          </Popover>
        </div>

        {/* Sort options - compact */}
        <Select
          value={`${sortBy}:${sortOrder}`}
          onValueChange={(value) => {
            const [newSortBy, newSortOrder] = value.split(':');
            setSortBy(newSortBy);
            setSortOrder(newSortOrder);
            // Auto-apply when sort changes
            setTimeout(() => applyFilters(), 0);
          }}
        >
          <SelectTrigger className={cn(
            "h-8 w-[110px] text-xs",
            isDarkMode ? "bg-gray-800 border-gray-700 text-gray-200" : "bg-white text-gray-700"
          )}>
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent className={isDarkMode ? "bg-gray-800 text-gray-200 border-gray-700" : "text-gray-700"}>
            <SelectItem value="created_at:-1">Newest First ↓</SelectItem>
            <SelectItem value="created_at:1">Oldest First ↑</SelectItem>
            <SelectItem value="input_content:1">Name (A-Z) ↑</SelectItem>
            <SelectItem value="input_content:-1">Name (Z-A) ↓</SelectItem>
          </SelectContent>
        </Select>

        {/* Action buttons - compact */}
        <div className="flex items-center gap-1 ml-auto">
          <Button
            variant="outline"
            onClick={resetFilters}
            size="sm"
            className={cn(
              "h-8 px-2 text-xs",
              isDarkMode ? "border-gray-700 text-gray-200 hover:bg-gray-700" : ""
            )}
          >
            <X className={cn(
              "h-3.5 w-3.5 mr-1",
              isDarkMode ? "text-gray-300" : "text-gray-600"
            )} />
            Reset
          </Button>
          <Button
            onClick={applyFilters}
            size="sm"
            className={cn(
              "h-8 px-3 text-xs",
              isDarkMode ? "bg-primary text-gray-100 hover:bg-primary/90" : "text-gray-100"
            )}
          >
            Apply
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

export default TaskFilter;
