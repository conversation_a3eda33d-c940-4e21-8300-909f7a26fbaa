import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Loader2,
  RefreshCw,
  Headphones,
  CheckCircle,
  Clock,
  Filter,
  ChevronRight,
  Music,
  Award,
  AlertCircle
} from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { StaggeredChildren } from '@/components/common/AnimatedPage';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { taskListService, TaskSetFilter, TaskSet } from '@/api/taskListService';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import TaskFilter from '../components/TaskFilter';
import TaskPagination from '../components/TaskPagination';

/**
 * Enhanced task list page with fixed filter, scrollable content, and fixed pagination
 */
const EnhancedTaskListPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [taskSets, setTaskSets] = useState<TaskSet[]>([]);
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Filter state
  const [filter, setFilter] = useState<TaskSetFilter>({
    page: 1,
    limit: 10,
    sort_by: 'created_at',
    sort_order: -1
  });

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Fetch task sets
  useEffect(() => {
    const fetchTaskSets = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        const response = await taskListService.fetchTaskSets(filter, {
          onSuccess: (data) => console.log('Task sets fetched successfully', data),
          onError: (err) => {
            console.error('Error fetching task sets', err);
            setError('Failed to load task sets. Please try again.');
          }
        });

        if (response && response.items) {
          setTaskSets(response.items);
          setTotalItems(response.total || response.items.length);
          setTotalPages(response.pages || Math.ceil((response.total || response.items.length) / filter.limit));
        }
      } catch (err) {
        console.error('Error fetching task sets:', err);
        setError('Failed to load task sets. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user) {
      fetchTaskSets();
    }
  }, [isAuthenticated, user, filter]);

  // Handle filter changes
  const handleFilterChange = (newFilter: TaskSetFilter) => {
    setFilter(newFilter);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setFilter(prev => ({ ...prev, page }));
  };

  // Handle page size change
  const handlePageSizeChange = (limit: number) => {
    setFilter(prev => ({ ...prev, limit, page: 1 }));
  };

  // Navigate to task set details
  const handleTaskSetClick = (taskSetId: string) => {
    navigate(`/tasks/${taskSetId}`);
  };

  // Handle refresh
  const handleRefresh = () => {
    // Refetch with current filter
    setFilter(prev => ({ ...prev, _timestamp: Date.now() }));
  };

  // Animation variants
  const pageVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 0.3,
        when: "beforeChildren"
      }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.2 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  const cardHoverVariants = {
    initial: { scale: 1, boxShadow: "0 1px 3px rgba(0,0,0,0.1)" },
    hover: {
      scale: 1.02,
      boxShadow: "0 10px 30px -10px rgba(0,0,0,0.2)",
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 30
      }
    },
    tap: {
      scale: 0.98,
      transition: {
        type: "spring",
        stiffness: 800,
        damping: 40
      }
    }
  };

  // Format date as time ago (e.g., "2 hours ago", "3 days ago")
  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // Handle future dates
    if (diffInSeconds < 0) {
      return 'in the future';
    }

    // Less than a minute
    if (diffInSeconds < 60) {
      return `${diffInSeconds} second${diffInSeconds !== 1 ? 's' : ''} ago`;
    }

    // Less than an hour
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    }

    // Less than a day
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
    }

    // Less than a month
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
    }

    // Less than a year
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${diffInMonths} month${diffInMonths !== 1 ? 's' : ''} ago`;
    }

    // More than a year
    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears} year${diffInYears !== 1 ? 's' : ''} ago`;
  };

  // Calculate progress for a task set based on score/max_score
  const calculateProgress = (taskSet: TaskSet) => {
    // If we have score and max_score, use that for progress (points achieved)
    if (taskSet.score !== undefined && taskSet.max_score) {
      return Math.round((taskSet.score / taskSet.max_score) * 100);
    }

    // If we have scored value and tasks, use that for completion progress
    const totalTasks = taskSet.tasks?.length || 0;
    if (taskSet.scored !== undefined && totalTasks > 0) {
      return Math.round((taskSet.scored / totalTasks) * 100);
    }

    // Fallback to status-based calculation if neither is available
    const completedTasks = taskSet.tasks?.filter((task: any) => task.status === 'completed')?.length || 0;
    return totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  };

  return (
    <MainLayout
      title="Task Sets"
      description="View and manage your learning tasks"
    >
      <AnimatePresence mode="wait">
        <motion.div
          className="flex flex-col h-[calc(100vh-4rem)]"
          initial="initial"
          animate="animate"
          exit="exit"
          variants={pageVariants}
        >
          {/* Fixed Header - Filter */}
          <div className={cn(
            "sticky top-0 z-30 border-b px-4 py-3 shadow-md",
            isDarkMode
              ? "bg-gray-900/95 border-gray-700 backdrop-blur-sm"
              : "bg-white/95 border-gray-200 backdrop-blur-sm"
          )}>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "flex items-center justify-center h-8 w-8 rounded-md",
                    isDarkMode ? "bg-blue-900/30" : "bg-blue-100"
                  )}>
                    <Award className={cn(
                      "h-4 w-4",
                      isDarkMode ? "text-blue-400" : "text-blue-600"
                    )} />
                  </div>
                  <h1 className={cn(
                    "text-lg font-bold",
                    isDarkMode ? "text-white" : "text-gray-800"
                  )}>
                    Task Sets
                  </h1>
                </div>

                <Button
                  variant="outline"
                  onClick={handleRefresh}
                  disabled={loading}
                  size="sm"
                  className={cn(
                    "h-8 px-3 text-xs flex items-center gap-1.5 transition-all duration-200",
                    isDarkMode
                      ? "border-gray-700 text-gray-200 hover:bg-gray-800 hover:border-gray-600"
                      : "hover:bg-gray-50 hover:text-gray-900"
                  )}
                >
                  {loading ? (
                    <Loader2 className="h-3.5 w-3.5 animate-spin" />
                  ) : (
                    <RefreshCw className="h-3.5 w-3.5" />
                  )}
                  <span>{loading ? "Loading..." : "Refresh"}</span>
                </Button>
              </div>

              <div className="w-full sm:w-auto">
                <TaskFilter filter={filter} onFilterChange={handleFilterChange} />
              </div>
            </div>
          </div>

          {/* Scrollable Content */}
          <div className={cn(
            "flex-1 overflow-y-auto p-4 md:p-6 relative",
            isDarkMode
              ? "bg-gray-900 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-800"
              : "bg-gray-50 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
          )}>
            {/* Background overlay to prevent content from showing through header/footer */}
            <div className="absolute inset-0 bg-inherit -z-10"></div>
          {/* Error state */}
          {error && !loading && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className={cn(
                "p-6 rounded-lg border shadow-sm mb-6",
                isDarkMode
                  ? "bg-red-900/20 border-red-800/30 text-red-200"
                  : "bg-red-50 border-red-200/50 text-red-800"
              )}
            >
              <div className="flex items-start gap-3">
                <div className={cn(
                  "p-2 rounded-full",
                  isDarkMode ? "bg-red-800/30" : "bg-red-100"
                )}>
                  <AlertCircle className={cn(
                    "h-5 w-5",
                    isDarkMode ? "text-red-300" : "text-red-600"
                  )} />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-medium mb-1">Error Loading Tasks</h3>
                  <p className="mb-4 opacity-90">{error}</p>
                  <Button
                    variant="outline"
                    className={cn(
                      "mt-1 border-current",
                      isDarkMode
                        ? "hover:bg-red-800/30 text-red-300"
                        : "hover:bg-red-100 text-red-700"
                    )}
                    onClick={handleRefresh}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Loading state */}
          {loading ? (
            <div className="py-12">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
                className="flex flex-col items-center justify-center"
              >
                <div className={cn(
                  "relative h-16 w-16 mb-6",
                  isDarkMode ? "text-blue-400" : "text-blue-600"
                )}>
                  <Loader2 className="h-16 w-16 animate-spin absolute" />
                  <Music className="h-6 w-6 absolute inset-0 m-auto opacity-70" />
                </div>
                <h3 className={cn(
                  "text-lg font-medium mb-1",
                  isDarkMode ? "text-gray-200" : "text-gray-700"
                )}>
                  Loading Task Sets
                </h3>
                <p className={cn(
                  "text-sm",
                  isDarkMode ? "text-gray-400" : "text-gray-500"
                )}>
                  Please wait while we fetch your learning tasks...
                </p>
              </motion.div>
            </div>
          ) : taskSets.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className={cn(
                "text-center py-16 px-6 rounded-xl border shadow-sm max-w-md mx-auto",
                isDarkMode
                  ? "bg-gray-800/50 border-gray-700"
                  : "bg-white border-gray-200"
              )}
            >
              <div className={cn(
                "inline-flex items-center justify-center h-16 w-16 rounded-full mb-4",
                isDarkMode ? "bg-gray-700" : "bg-gray-100"
              )}>
                <Filter className={cn(
                  "h-8 w-8",
                  isDarkMode ? "text-gray-400" : "text-gray-500"
                )} />
              </div>
              <h3 className={cn(
                "text-xl font-medium mb-2",
                isDarkMode ? "text-gray-200" : "text-gray-800"
              )}>
                No task sets found
              </h3>
              <p className={cn(
                "mb-6 max-w-sm mx-auto",
                isDarkMode ? "text-gray-400" : "text-gray-500"
              )}>
                Try adjusting your filters or create new task sets to start your learning journey
              </p>
              <Button
                onClick={() => navigate('/begin-learning')}
                className="px-6 py-2 h-auto"
                size="lg"
              >
                Start Learning
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </motion.div>
          ) : (
            <StaggeredChildren className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {taskSets.map((taskSet) => {
                // Calculate progress
                const progress = calculateProgress(taskSet);

                return (
                  <motion.div
                    key={taskSet.id || taskSet._id}
                    initial="initial"
                    whileHover="hover"
                    whileTap="tap"
                    variants={cardHoverVariants}
                  >
                    <Card
                      className={cn(
                        "cursor-pointer overflow-hidden transition-all duration-300 border",
                        isDarkMode
                          ? "bg-gray-800 border-gray-700 hover:border-gray-600"
                          : "bg-white hover:border-gray-300"
                      )}
                      onClick={() => handleTaskSetClick(taskSet.id || taskSet._id)}
                    >
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className={cn(
                            "text-lg",
                            isDarkMode ? "text-gray-100" : "text-gray-700"
                          )}>
                            {taskSet.input_content || 'Task Set'}
                          </CardTitle>

                          <div className="flex items-center space-x-2">
                            {/* Input Type Tooltip */}
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className={cn(
                                    "flex items-center justify-center h-6 w-6 rounded-full",
                                    taskSet.input_type === 'audio'
                                      ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                                      : "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400"
                                  )}>
                                    {taskSet.input_type === 'audio' ? (
                                      <Headphones className="h-3.5 w-3.5" />
                                    ) : (
                                      <span className="text-xs font-bold">{taskSet.input_type?.charAt(0).toUpperCase()}</span>
                                    )}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Input Type: {taskSet.input_type || 'Unknown'}</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>

                            {/* Status Tooltip */}
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className={cn(
                                    "flex items-center justify-center h-6 w-6 rounded-full",
                                    taskSet.status === 'completed'
                                      ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
                                      : "bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400"
                                  )}>
                                    {taskSet.status === 'completed' ? (
                                      <CheckCircle className="h-3.5 w-3.5" />
                                    ) : (
                                      <Clock className="h-3.5 w-3.5" />
                                    )}
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Status: {taskSet.status || 'Pending'}</p>
                                  {taskSet.score !== undefined && taskSet.max_score !== undefined && (
                                    <p>Score: {taskSet.score}/{taskSet.max_score}</p>
                                  )}
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex justify-between items-center mb-4">
                          <p className={cn(
                            "text-sm flex items-center",
                            isDarkMode ? "text-gray-300" : "text-gray-500"
                          )}>
                            <Clock className="h-3.5 w-3.5 mr-1.5 inline" />
                            {new Date(taskSet.created_at).toLocaleDateString()}
                          </p>
                          <p className={cn(
                            "text-xs italic",
                            isDarkMode ? "text-gray-400" : "text-gray-500"
                          )}>
                            {formatTimeAgo(taskSet.created_at)}
                          </p>
                        </div>

                        {/* Status and Score */}
                        <div className={cn(
                          "flex items-center justify-between mb-4 px-3 py-2 rounded-md",
                          taskSet.status === 'completed'
                            ? isDarkMode ? "bg-green-900/20" : "bg-green-50"
                            : isDarkMode ? "bg-amber-900/20" : "bg-amber-50"
                        )}>
                          <div className="flex items-center">
                            {taskSet.status === 'completed' ? (
                              <CheckCircle className={cn(
                                "h-4 w-4 mr-2",
                                isDarkMode ? "text-green-400" : "text-green-600"
                              )} />
                            ) : (
                              <Clock className={cn(
                                "h-4 w-4 mr-2",
                                isDarkMode ? "text-amber-400" : "text-amber-600"
                              )} />
                            )}
                            <span className={cn(
                              "text-sm font-medium",
                              taskSet.status === 'completed'
                                ? isDarkMode ? "text-green-400" : "text-green-600"
                                : isDarkMode ? "text-amber-400" : "text-amber-600"
                            )}>
                              {taskSet?.status === 'completed' ? 'Completed' : 'Pending'}
                            </span>
                          </div>

                          <Badge variant={taskSet?.score !== undefined ? "success" : "secondary"} className="ml-auto">
                            {taskSet?.score !== undefined && taskSet.max_score && (
                              <span>
                                Scored: {taskSet?.score ?? 0}/{taskSet.max_score ?? 0}
                              </span>
                            )}
                          </Badge>
                        </div>

                        <div className="space-y-3">
                          {/* Progress bar */}
                          <div className="relative pt-1">
                            <div className={cn(
                              "overflow-hidden h-2 text-xs flex rounded-full",
                              isDarkMode ? "bg-gray-700" : "bg-gray-200"
                            )}>
                              <div
                                className={cn(
                                  "shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center transition-all duration-500",
                                  progress === 100
                                    ? "bg-gradient-to-r from-green-400 to-green-500"
                                    : progress > 50
                                      ? "bg-gradient-to-r from-blue-400 to-blue-500"
                                      : progress > 0
                                        ? "bg-gradient-to-r from-blue-300 to-blue-400"
                                        : "bg-gray-400"
                                )}
                                style={{ width: `${progress}%` }}
                              ></div>
                            </div>
                          </div>

                          {/* Progress stats */}
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <Badge
                                variant={(taskSet.scored ?? 0) > 0 ? "success" : "outline"}
                                className={cn(
                                  "mr-2 text-xs h-5 min-w-5 flex items-center justify-center",
                                  (taskSet.scored ?? 0) > 0
                                    ? ""
                                    : isDarkMode ? "bg-gray-800 text-gray-400" : "bg-gray-100 text-gray-500"
                                )}
                              >
                                {taskSet.scored ?? 0}
                              </Badge>
                              <span className={cn(
                                "text-xs",
                                isDarkMode ? "text-gray-400" : "text-gray-500"
                              )}>
                                of {taskSet.tasks?.length ?? 0} tasks scored
                              </span>
                            </div>

                            <span className={cn(
                              "text-xs font-medium",
                              progress === 100
                                ? "text-green-500"
                                : progress > 0
                                  ? "text-blue-500"
                                  : isDarkMode ? "text-gray-400" : "text-gray-600"
                            )}>
                              {progress}%
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </StaggeredChildren>
          )}
        </div>

        {/* Fixed Footer - Pagination */}
        <div className={cn(
          "sticky bottom-0 z-20 border-t px-4 py-2 shadow-[0_-2px_4px_rgba(0,0,0,0.1)]",
          isDarkMode ? "bg-gray-900 border-gray-700" : "bg-white border-gray-200"
        )}>
          <TaskPagination
            currentPage={filter.page}
            totalPages={totalPages}
            totalItems={totalItems}
            pageSize={filter.limit}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </div>
        </motion.div>
      </AnimatePresence>
    </MainLayout>
  );
};

export default EnhancedTaskListPage;
