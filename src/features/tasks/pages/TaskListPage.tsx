import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2 } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import AnimatedPage, { StaggeredChildren } from '@/components/common/AnimatedPage';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { taskListService } from '@/api/taskListService';

/**
 * Enhanced task list page with animations and theme support
 */
const TaskListPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [taskSets, setTaskSets] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const [error, setError] = useState<string | null>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Fetch task sets
  useEffect(() => {
    const fetchTaskSets = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        const response = await taskListService.fetchTaskSets({
          page: 1,
          limit: 20,
          sort_by: 'created_at',
          sort_order: -1
        }, {
          onSuccess: (data) => console.log('Task sets fetched successfully', data),
          onError: (err) => {
            console.error('Error fetching task sets', err);
            setError('Failed to load task sets. Please try again.');
          }
        });

        if (response && response.items) {
          setTaskSets(response.items);
        }
      } catch (err) {
        console.error('Error fetching task sets:', err);
        setError('Failed to load task sets. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated && user) {
      fetchTaskSets();
    }
  }, [isAuthenticated, user]);

  // Filter task sets based on active tab
  const filteredTaskSets = taskSets.filter(taskSet => {
    if (activeTab === 'all') return true;

    // Calculate progress
    const totalTasks = taskSet.tasks?.length || 0;
    const completedTasks = taskSet.tasks?.filter((task: any) => task.status === 'completed')?.length || 0;
    const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    if (activeTab === 'completed') return progress === 100;
    if (activeTab === 'in-progress') return progress > 0 && progress < 100;
    if (activeTab === 'not-started') return progress === 0;
    return true;
  });

  // Navigate to task set details
  const handleTaskSetClick = (taskSetId: string) => {
    navigate(`/tasks/${taskSetId}`);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <MainLayout
      title="Task Sets"
      description="View and manage your learning tasks"
    >
      <AnimatedPage>
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Task filters */}
          <motion.div variants={itemVariants}>
            <Tabs
              defaultValue="all"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-4 md:w-auto">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="in-progress">In Progress</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
                <TabsTrigger value="not-started">Not Started</TabsTrigger>
              </TabsList>
            </Tabs>
          </motion.div>

          {/* Error state */}
          {error && !loading && (
            <motion.div
              variants={itemVariants}
              className={cn(
                "p-4 rounded-md",
                isDarkMode ? "bg-red-900/30 text-red-200" : "bg-red-50 text-red-800"
              )}
            >
              <p>{error}</p>
              <Button
                variant="outline"
                className="mt-2"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </motion.div>
          )}

          {/* Task sets */}
          {loading ? (
            <motion.div
              variants={itemVariants}
              className="flex justify-center items-center py-12"
            >
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </motion.div>
          ) : filteredTaskSets.length === 0 ? (
            <motion.div
              variants={itemVariants}
              className={cn(
                "text-center py-12 rounded-lg border",
                isDarkMode
                  ? "bg-gray-800/50 border-gray-700"
                  : "bg-gray-50 border-gray-200"
              )}
            >
              <h3 className="text-lg font-medium mb-2">No task sets found</h3>
              <p className={cn(
                "mb-4",
                isDarkMode ? "text-gray-400" : "text-gray-500"
              )}>
                {activeTab === 'all'
                  ? "You don't have any task sets yet."
                  : `You don't have any ${activeTab.replace('-', ' ')} task sets.`}
              </p>
              <Button onClick={() => navigate('/begin-learning')}>
                Start Learning
              </Button>
            </motion.div>
          ) : (
            <StaggeredChildren className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTaskSets.map((taskSet) => {
                // Calculate progress
                const totalTasks = taskSet.tasks?.length || 0;
                const completedTasks = taskSet.tasks?.filter((task: any) => task.status === 'completed')?.length || 0;
                const progress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

                return (
                  <motion.div
                    key={taskSet.id || taskSet._id}
                    whileHover={{ scale: 1.02, boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)" }}
                    transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  >
                    <Card
                      className={cn(
                        "cursor-pointer hover:shadow-md transition-shadow",
                        isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
                      )}
                      onClick={() => handleTaskSetClick(taskSet.id || taskSet._id)}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className={cn(
                          "text-lg",
                          isDarkMode ? "text-gray-100" : "text-gray-800"
                        )}>
                          {taskSet.input_content || 'Task Set'}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className={cn(
                          "text-sm mb-4",
                          isDarkMode ? "text-gray-300" : "text-gray-500"
                        )}>
                          {taskSet.description || `Created on ${new Date(taskSet.created_at).toLocaleDateString()}`}
                        </p>

                        <div className="space-y-2">
                          {/* Progress bar */}
                          <div className={cn(
                            "w-full rounded-full h-2.5",
                            isDarkMode ? "bg-gray-700" : "bg-gray-200"
                          )}>
                            <div
                              className={cn(
                                "h-2.5 rounded-full",
                                progress === 100
                                  ? "bg-green-500"
                                  : progress > 0
                                    ? "bg-blue-500"
                                    : "bg-gray-400"
                              )}
                              style={{ width: `${progress}%` }}
                            ></div>
                          </div>

                          {/* Progress stats */}
                          <div className="flex justify-between text-xs">
                            <span className={cn(
                              isDarkMode ? "text-gray-300" : "text-gray-500"
                            )}>
                              {completedTasks} of {totalTasks} tasks completed
                            </span>
                            <span className={cn(
                              "font-medium",
                              progress === 100
                                ? "text-green-500"
                                : progress > 0
                                  ? "text-blue-500"
                                  : isDarkMode ? "text-gray-400" : "text-gray-600"
                            )}>
                              {progress}%
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </StaggeredChildren>
          )}
        </motion.div>
      </AnimatedPage>
    </MainLayout>
  );
};

export default TaskListPage;
