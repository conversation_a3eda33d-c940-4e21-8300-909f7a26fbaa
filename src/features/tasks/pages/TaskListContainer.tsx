import React, { useState } from 'react';
import SideNavigation from '@/features/navigation/components/SideNavigation';
import TaskListWithCaching from '@/features/tasks/components/TaskListWithCaching';
import TaskListWithRedux from '@/features/tasks/components/TaskListWithRedux';
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';

/**
 * Enhanced task list container with animations and theme support
 */
const TaskListContainer: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('caching');
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const headerVariants = {
    hidden: { y: -20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { 
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <div className="flex min-h-screen">
      <SideNavigation />
      
      <motion.main 
        className="flex-1 overflow-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div 
          className={cn(
            "p-4 border-b",
            isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"
          )}
          variants={headerVariants}
        >
          <h1 className={cn(
            "text-2xl font-bold",
            isDarkMode ? "text-white" : "text-gray-900"
          )}>
            Task List
          </h1>
          <p className={cn(
            "text-sm mt-1",
            isDarkMode ? "text-gray-300" : "text-gray-500"
          )}>
            View and manage your task sets
          </p>
        </motion.div>
        
        <div className="p-4">
          <Tabs
            defaultValue="caching"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="mb-4">
              <TabsTrigger value="caching">With Caching</TabsTrigger>
              <TabsTrigger value="redux">With Redux</TabsTrigger>
            </TabsList>
            
            <TabsContent value="caching" className="mt-0">
              <TaskListWithCaching />
            </TabsContent>
            
            <TabsContent value="redux" className="mt-0">
              <TaskListWithRedux />
            </TabsContent>
          </Tabs>
        </div>
      </motion.main>
    </div>
  );
};

export default TaskListContainer;
