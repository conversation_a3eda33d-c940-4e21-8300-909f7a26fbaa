# Gallery Feature

The Gallery feature provides a comprehensive content discovery and management system with three main pages:

## Pages

### 📚 Anthology (`/anthology`)
**Curated content collection page**

- Displays filtered content sets using the `/v1/management/curated/filtered` API
- Advanced filtering by theme, difficulty, status, and type
- Search functionality (URL parameter ready)
- Responsive card grid layout
- Pagination support
- Direct navigation to content sets

**Key Features:**
- Real-time filter updates with URL synchronization
- Theme-based filtering with visual indicators
- Content cards with rich metadata display
- Smooth animations and loading states

### 🎨 Atelier (`/atelier`)
**Theme discovery and exploration page**

- Displays all available themes using the `/v1/management/curated/themes` API
- Category-based filtering
- Grid/List view toggle
- Rich theme cards with statistics
- Direct theme selection for Anthology filtering

**Key Features:**
- Beautiful theme cards with color accents and icons
- Statistics display (content sets, items, averages)
- Category filtering and search
- Smooth navigation to Anthology with pre-applied filters

### ⚡ Playground (`/playground`)
**Admin content generation workspace**

- Admin-only access with role-based authentication
- Prompt-based content generation using `/v1/management/curated/generate`
- Generation history with status tracking
- Real-time status updates

**Key Features:**
- Rich text prompt editor with character counting
- Generation status tracking and history
- Admin role verification
- Responsive design for content management

## Components

### Shared Components

#### `ContentCard`
- Displays content set information in card format
- Theme-aware styling with color accents
- Difficulty and status badges
- Hover animations and interactions

#### `ThemeCard`
- Rich theme display with statistics
- Color-coded category badges
- Interactive hover effects
- Statistics visualization

#### `FilterPanel`
- Comprehensive filtering interface
- Real-time filter application
- Active filter display with removal options
- Integration with filter options API

#### `SearchBar`
- Debounced search input
- Clear functionality
- Responsive design

## API Integration

### Service Layer
The feature uses `curatedContentService` which extends `BaseService`:

```typescript
import { curatedContentService } from '@/api/curatedContentService';

// Get themes
const themes = await curatedContentService.getThemes(filter);

// Get filtered content
const content = await curatedContentService.getFilteredContent(filter);

// Generate content (admin only)
const result = await curatedContentService.generateContent(prompt);
```

### API Endpoints Used
- `GET /v1/management/curated/themes` - Theme listing
- `GET /v1/management/curated/filtered` - Filtered content
- `GET /v1/management/curated/filter-options` - Filter options
- `POST /v1/management/curated/generate` - Content generation
- `POST /v1/management/curated/get_prompts` - Prompt history

## Navigation Integration

### Menu Items
- **Anthology**: Main navigation item for all users
- **Atelier**: Theme discovery for all users  
- **Playground**: Admin-only section with visual separation

### Routing
- `/anthology` - Content collection with URL-based filtering
- `/atelier` - Theme gallery
- `/playground` - Admin content generation

### Breadcrumbs
All pages integrate with the existing breadcrumb system.

## Design System

### Color Consistency
- Uses existing theme colors (blues/purples)
- Theme-specific accent colors from API data
- Consistent with current application design

### Animations
- Framer Motion for smooth transitions
- Loading states and skeleton screens
- Hover effects and micro-interactions

### Responsive Design
- Mobile-first approach
- Flexible grid layouts
- Adaptive navigation

## Usage Examples

### Basic Content Browsing
```typescript
// Navigate to Anthology
navigate('/anthology');

// Navigate with theme filter
navigate('/anthology?theme_id=507f1f77bcf86cd799439011');

// Navigate with multiple filters
navigate('/anthology?theme_id=507f1f77bcf86cd799439011&difficulty_level=2&status=completed');
```

### Theme Selection Flow
```typescript
// From Atelier to Anthology with theme
const handleThemeClick = (theme: Theme) => {
  navigate(`/anthology?theme_id=${theme._id}`);
};
```

### Admin Content Generation
```typescript
// Generate content (admin only)
const generateContent = async (prompt: string) => {
  const result = await curatedContentService.generateContent({
    content: prompt
  });
};
```

## File Structure

```
src/features/gallery/
├── components/
│   ├── ContentCard.tsx      # Content set display
│   ├── ThemeCard.tsx        # Theme display
│   ├── FilterPanel.tsx      # Filtering interface
│   ├── SearchBar.tsx        # Search functionality
│   └── index.ts             # Barrel exports
├── pages/
│   ├── AnthologyPage.tsx    # Main content collection
│   ├── AtelierPage.tsx      # Theme discovery
│   ├── PlaygroundPage.tsx   # Admin generation
│   └── index.ts             # Barrel exports
├── index.ts                 # Feature barrel export
└── README.md               # This file
```

## Future Enhancements

- Search functionality implementation in API
- Content set detail pages
- Bookmark/favorites system
- Advanced analytics for admin
- Bulk content operations
- Export functionality
