import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { ThemeCard, SearchBar } from '../components';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ThemesFilter, 
  Theme, 
  ApiResponse, 
  curatedContentService 
} from '@/api/curatedContentService';
import { useApi } from '@/context/ApiContext';
import { Palette, RefreshCw, AlertCircle, Filter, Grid, List } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Atelier page - Theme discovery and selection
 */
const AtelierPage: React.FC = () => {
  const navigate = useNavigate();
  const { handleError } = useApi();

  // State management
  const [themes, setThemes] = useState<Theme[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    total_pages: 0
  });

  const [filters, setFilters] = useState<ThemesFilter>({
    page: 1,
    limit: 50,
    is_active: true
  });

  const [searchQuery, setSearchQuery] = useState('');

  // Available categories for filtering
  const categories = [
    'culture', 'geography', 'history', 'language', 'literature', 
    'science', 'sports', 'politics', 'economy', 'religion', 
    'art', 'music', 'dance', 'food', 'festivals'
  ];

  // Load themes when filters change
  useEffect(() => {
    loadThemes();
  }, [filters]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery !== filters.search) {
        setFilters(prev => ({ ...prev, search: searchQuery || undefined, page: 1 }));
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, filters.search]);

  const loadThemes = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response: ApiResponse<Theme> = await curatedContentService.getThemes(filters);
      setThemes(response.data);
      setPagination(response.meta);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to load themes';
      setError(errorMessage);
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleThemeClick = (theme: Theme) => {
    // Navigate to Anthology with theme filter
    navigate(`/anthology?theme_id=${theme._id}`);
  };

  const handleCategoryFilter = (category: string) => {
    setFilters(prev => ({ 
      ...prev, 
      category: category === 'all' ? undefined : category, 
      page: 1 
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const getActiveFiltersCount = () => {
    return Object.keys(filters).filter(key => 
      key !== 'page' && key !== 'limit' && key !== 'is_active' && 
      filters[key as keyof ThemesFilter] !== undefined
    ).length;
  };

  const renderPagination = () => {
    if (pagination.total_pages <= 1) return null;

    return (
      <div className="flex items-center justify-center gap-2 mt-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.page - 1)}
          disabled={pagination.page <= 1}
        >
          Previous
        </Button>
        
        <span className="text-sm text-muted-foreground px-4">
          Page {pagination.page} of {pagination.total_pages} ({pagination.total} themes)
        </span>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.page + 1)}
          disabled={pagination.page >= pagination.total_pages}
        >
          Next
        </Button>
      </div>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className={cn(
          'grid gap-6',
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        )}>
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-80 w-full" />
          ))}
        </div>
      );
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={loadThemes}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    if (themes.length === 0) {
      return (
        <div className="text-center py-12">
          <Palette className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No themes found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search or category filter.
          </p>
          <Button onClick={() => setFilters({ page: 1, limit: 50, is_active: true })} variant="outline">
            Clear Filters
          </Button>
        </div>
      );
    }

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className={cn(
          'grid gap-6',
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1 max-w-4xl mx-auto'
        )}
      >
        <AnimatePresence mode="popLayout">
          {themes.map((theme) => (
            <ThemeCard
              key={theme._id}
              theme={theme}
              onClick={() => handleThemeClick(theme)}
              className="w-full"
            />
          ))}
        </AnimatePresence>
      </motion.div>
    );
  };

  return (
    <MainLayout
      title="🎨 Atelier"
      description="Discover and explore learning themes"
      showBreadcrumbs={true}
    >
      <div className="space-y-6">
        {/* Header Controls */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <SearchBar
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder="Search themes..."
            className="flex-1 max-w-md"
          />
          
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">Category:</span>
            <Select
              value={filters.category || 'all'}
              onValueChange={handleCategoryFilter}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary">
              {getActiveFiltersCount()} filter{getActiveFiltersCount() > 1 ? 's' : ''} active
            </Badge>
          )}
        </div>

        {/* Content */}
        {renderContent()}

        {/* Pagination */}
        {renderPagination()}
      </div>
    </MainLayout>
  );
};

export default AtelierPage;
