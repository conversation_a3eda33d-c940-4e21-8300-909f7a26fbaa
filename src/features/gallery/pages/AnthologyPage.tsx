import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useSearchParams } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { ContentCard, FilterPanel, SearchBar } from '../components';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  ContentFilter, 
  ContentSet, 
  ApiResponse, 
  curatedContentService 
} from '@/api/curatedContentService';
import { useApi } from '@/context/ApiContext';
import { BookOpen, RefreshCw, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Anthology page - Main curated content collection
 */
const AnthologyPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { handleError } = useApi();

  // State management
  const [content, setContent] = useState<ContentSet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    total_pages: 0
  });

  // Initialize filters from URL params
  const [filters, setFilters] = useState<ContentFilter>(() => {
    const initialFilters: ContentFilter = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20')
    };

    // Add other filters from URL params
    const themeId = searchParams.get('theme_id');
    const difficulty = searchParams.get('difficulty_level');
    const status = searchParams.get('status');
    const gentype = searchParams.get('gentype');

    if (themeId) initialFilters.theme_id = themeId;
    if (difficulty) initialFilters.difficulty_level = parseInt(difficulty);
    if (status) initialFilters.status = status;
    if (gentype) initialFilters.gentype = gentype;

    return initialFilters;
  });

  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');

  // Load content when filters change
  useEffect(() => {
    loadContent();
  }, [filters]);

  // Update URL params when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });

    if (searchQuery) {
      params.set('search', searchQuery);
    }

    setSearchParams(params);
  }, [filters, searchQuery, setSearchParams]);

  const loadContent = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response: ApiResponse<ContentSet> = await curatedContentService.getFilteredContent(filters);
      setContent(response.data);
      setPagination(response.meta);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to load content';
      setError(errorMessage);
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFiltersChange = (newFilters: ContentFilter) => {
    setFilters({ ...newFilters, page: 1 }); // Reset to first page when filters change
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    // Note: Search functionality would need to be implemented in the API
    // For now, we'll just update the URL param
  };

  const handleViewThemes = () => {
    navigate('/atelier');
  };

  const handleContentClick = (contentSet: ContentSet) => {
    // Navigate to content detail or start learning
    navigate(`/tasks/${contentSet._id}`);
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const renderPagination = () => {
    if (pagination.total_pages <= 1) return null;

    return (
      <div className="flex items-center justify-center gap-2 mt-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.page - 1)}
          disabled={pagination.page <= 1}
        >
          Previous
        </Button>
        
        <span className="text-sm text-muted-foreground px-4">
          Page {pagination.page} of {pagination.total_pages} ({pagination.total} items)
        </span>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.page + 1)}
          disabled={pagination.page >= pagination.total_pages}
        >
          Next
        </Button>
      </div>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-64 w-full" />
          ))}
        </div>
      );
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={loadContent}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      );
    }

    if (content.length === 0) {
      return (
        <div className="text-center py-12">
          <BookOpen className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No content found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your filters or explore different themes.
          </p>
          <Button onClick={handleViewThemes} variant="outline">
            Browse Themes
          </Button>
        </div>
      );
    }

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <AnimatePresence mode="popLayout">
          {content.map((item, index) => (
            <ContentCard
              key={item._id}
              content={item}
              onClick={() => handleContentClick(item)}
              className="w-full"
            />
          ))}
        </AnimatePresence>
      </motion.div>
    );
  };

  return (
    <MainLayout
      title="📚 Anthology"
      description="Discover curated learning content"
      showBreadcrumbs={true}
    >
      <div className="space-y-6">
        {/* Search Bar */}
        <SearchBar
          value={searchQuery}
          onChange={handleSearchChange}
          placeholder="Search content by title or description..."
          className="max-w-md"
        />

        {/* Filter Panel */}
        <FilterPanel
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onViewThemes={handleViewThemes}
        />

        {/* Content Grid */}
        {renderContent()}

        {/* Pagination */}
        {renderPagination()}
      </div>
    </MainLayout>
  );
};

export default AnthologyPage;
