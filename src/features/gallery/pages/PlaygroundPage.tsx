import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  GeneratePromptRequest, 
  Prompt, 
  ApiResponse, 
  curatedContentService 
} from '@/api/curatedContentService';
import { useApi } from '@/context/ApiContext';
import { useAuth } from '@/context/AuthContext';
import { 
  Zap, 
  Send, 
  History, 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  X,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Playground page - Admin content generation interface
 */
const PlaygroundPage: React.FC = () => {
  const { user } = useAuth();
  const { handleSuccess, handleError } = useApi();

  // State management
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [isLoadingPrompts, setIsLoadingPrompts] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if user has admin access (you may need to adjust this based on your auth system)
  const isAdmin = user?.role === 'admin' || user?.role === 'agent';

  useEffect(() => {
    if (isAdmin) {
      loadPrompts();
    }
  }, [isAdmin]);

  const loadPrompts = async () => {
    setIsLoadingPrompts(true);
    try {
      const response: ApiResponse<Prompt> = await curatedContentService.getPrompts();
      setPrompts(response.data);
    } catch (err: any) {
      console.error('Failed to load prompts:', err);
      handleError(err);
    } finally {
      setIsLoadingPrompts(false);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const request: GeneratePromptRequest = {
        content: prompt.trim()
      };

      const response = await curatedContentService.generateContent(request);
      
      handleSuccess(response, 'Content generation started successfully!');
      setPrompt(''); // Clear the prompt
      
      // Reload prompts to show the new one
      await loadPrompts();
      
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to generate content';
      setError(errorMessage);
      handleError(err);
    } finally {
      setIsGenerating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending':
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'failed':
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'pending':
      case 'in_progress':
        return <Clock className="w-4 h-4" />;
      case 'failed':
      case 'error':
        return <X className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Redirect if not admin
  if (!isAdmin) {
    return (
      <MainLayout
        title="⚡ Playground"
        description="Content generation workspace"
        showBreadcrumbs={true}
      >
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Access denied. This page is only available to administrators.
          </AlertDescription>
        </Alert>
      </MainLayout>
    );
  }

  return (
    <MainLayout
      title="⚡ Playground"
      description="Content generation workspace"
      showBreadcrumbs={true}
    >
      <div className="space-y-6">
        {/* Generation Interface */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="w-5 h-5 text-yellow-500" />
              Content Generator
            </CardTitle>
            <CardDescription>
              Write a prompt to generate new learning content. Be specific about the topic, difficulty level, and type of content you want.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="prompt" className="text-sm font-medium">
                Prompt
              </label>
              <Textarea
                id="prompt"
                placeholder="Example: Generate 10 multiple choice questions about Nepali festivals for intermediate level learners. Include questions about Dashain, Tihar, and Holi with cultural context and explanations."
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                rows={6}
                className="resize-none"
              />
              <p className="text-xs text-muted-foreground">
                {prompt.length}/1000 characters
              </p>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end">
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !prompt.trim()}
                className="min-w-32"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Generate
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Separator />

        {/* Prompt History */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <History className="w-5 h-5" />
                Generation History
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={loadPrompts}
                disabled={isLoadingPrompts}
              >
                {isLoadingPrompts ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </Button>
            </div>
            <CardDescription>
              View your recent content generation requests and their status.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingPrompts ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin mr-2" />
                Loading history...
              </div>
            ) : prompts.length === 0 ? (
              <div className="text-center py-8">
                <Zap className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  No generation history yet. Create your first prompt above!
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <AnimatePresence>
                  {prompts.map((promptItem) => (
                    <motion.div
                      key={promptItem._id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="border rounded-lg p-4 space-y-3"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="text-sm leading-relaxed line-clamp-3">
                            {promptItem.content}
                          </p>
                        </div>
                        <Badge className={cn('ml-4 flex items-center gap-1', getStatusColor(promptItem.status))}>
                          {getStatusIcon(promptItem.status)}
                          {promptItem.status}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>Created {formatDate(promptItem.created_at)}</span>
                        {promptItem.task_set_id && (
                          <span>Task Set: {promptItem.task_set_id}</span>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};

export default PlaygroundPage;
