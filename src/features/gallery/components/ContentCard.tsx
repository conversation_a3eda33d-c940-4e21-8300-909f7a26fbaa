import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, BookOpen, Star, Play } from 'lucide-react';
import { ContentSet } from '@/api/curatedContentService';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';

interface ContentCardProps {
  content: ContentSet;
  onClick?: () => void;
  className?: string;
}

/**
 * ContentCard component for displaying content sets in the Anthology
 */
const ContentCard: React.FC<ContentCardProps> = ({
  content,
  onClick,
  className
}) => {
  const { isDarkMode } = useTheme();

  const getDifficultyColor = (level: number) => {
    switch (level) {
      case 1: return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 2: return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 3: return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'in_progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'pending': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className={className}
    >
      <Card
        className={cn(
          'group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
          'border-l-4 border-l-transparent hover:border-l-blue-500',
          isDarkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white hover:bg-gray-50',
          content.theme?.color && `hover:border-l-[${content.theme.color}]`
        )}
        onClick={onClick}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {content.theme?.icon && (
                  <span className="text-lg">{content.theme.icon}</span>
                )}
                <Badge variant="outline" className="text-xs">
                  {content.theme?.name_en || content.theme?.name}
                </Badge>
              </div>
              <CardTitle className="text-lg line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {content.title_en || content.title}
              </CardTitle>
            </div>
            <div className="flex flex-col gap-1 ml-2">
              <Badge className={getDifficultyColor(content.difficulty_level)}>
                Level {content.difficulty_level}
              </Badge>
              <Badge className={getStatusColor(content.status)}>
                {content.status.replace(/_/g, ' ')}
              </Badge>
            </div>
          </div>
          <CardDescription className="line-clamp-2 text-sm">
            {content.description_en || content.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                <BookOpen className="w-4 h-4" />
                <span>{content.total_items} items</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>{formatDate(content.created_at)}</span>
              </div>
            </div>
            <Badge variant="secondary" className="text-xs">
              {content.gentype}
            </Badge>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {content.theme?.color && (
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: content.theme.color }}
                />
              )}
              <span className="text-sm text-muted-foreground">
                {content.theme?.category}
              </span>
            </div>
            <Button
              size="sm"
              variant="ghost"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <Play className="w-4 h-4 mr-1" />
              Start
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ContentCard;
