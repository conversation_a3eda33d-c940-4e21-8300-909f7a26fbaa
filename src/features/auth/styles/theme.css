/* Modern authentication theme colors */

:root {
  --nepali-red: #DC143C;
  --nepali-red-dark: #8B0000;
  --nepali-red-light: #FF6B81;
  --nepali-blue: #003893;
  --nepali-blue-dark: #002D5A;
  --nepali-blue-light: #0055CC;
  --nepali-orange: #FF8C00;
  --nepali-orange-dark: #D97706;
  --nepali-orange-light: #FDBA74;
  --nepali-maroon: #800000;
  --nepali-maroon-light: #A52A2A;
}

/* Light theme colors */
.auth-light-theme {
  --auth-bg-primary: #FFFFFF;
  --auth-bg-secondary: #F9FAFB;
  --auth-text-primary: #111827;
  --auth-text-secondary: #4B5563;
  --auth-text-muted: #6B7280;
  --auth-border: #E5E7EB;
  --auth-input-bg: #FFFFFF;
  --auth-input-border: #D1D5DB;
  --auth-input-focus: var(--nepali-red);
  --auth-button-bg: linear-gradient(135deg, var(--nepali-red) 0%, var(--nepali-red-dark) 100%);
  --auth-button-hover: linear-gradient(135deg, var(--nepali-red-light) 0%, var(--nepali-red) 100%);
  --auth-accent: var(--nepali-red);
  --auth-accent-hover: var(--nepali-red-light);
  --auth-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --auth-card-bg: #FFFFFF;
}

/* Dark theme colors */
.auth-dark-theme {
  --auth-bg-primary: #111827;
  --auth-bg-secondary: #1F2937;
  --auth-text-primary: #F9FAFB;
  --auth-text-secondary: #E5E7EB;
  --auth-text-muted: #9CA3AF;
  --auth-border: #374151;
  --auth-input-bg: #1F2937;
  --auth-input-border: #374151;
  --auth-input-focus: var(--nepali-red-light);
  --auth-button-bg: linear-gradient(135deg, var(--nepali-red) 0%, var(--nepali-red-dark) 100%);
  --auth-button-hover: linear-gradient(135deg, var(--nepali-red-light) 0%, var(--nepali-red) 100%);
  --auth-accent: var(--nepali-red-light);
  --auth-accent-hover: var(--nepali-red);
  --auth-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  --auth-card-bg: #1F2937;
}

/* Gradient backgrounds */
.nepali-gradient-red {
  background: linear-gradient(135deg, var(--nepali-red) 0%, var(--nepali-red-dark) 100%);
}

.nepali-gradient-blue {
  background: linear-gradient(135deg, var(--nepali-blue) 0%, var(--nepali-blue-dark) 100%);
}

.nepali-gradient-orange {
  background: linear-gradient(135deg, var(--nepali-orange) 0%, var(--nepali-orange-dark) 100%);
}

.nepali-gradient-mixed {
  background: linear-gradient(135deg, var(--nepali-red) 0%, var(--nepali-blue) 100%);
}

/* Background patterns */
.auth-pattern {
  background-color: var(--auth-bg-secondary);
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23dc143c' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Login background */
.login-background {
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.05) 0%, rgba(0, 56, 147, 0.05) 100%);
}

/* Card styles */
.auth-card {
  background-color: var(--auth-card-bg);
  border-radius: 1rem;
  box-shadow: var(--auth-shadow);
  overflow: hidden;
  transition: all 0.3s ease;
}

.auth-card:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Input styles */
.auth-input {
  background-color: var(--auth-input-bg);
  border-color: var(--auth-input-border);
  color: var(--auth-text-primary);
  transition: all 0.2s ease;
}

.auth-input:focus {
  border-color: var(--auth-input-focus);
  box-shadow: 0 0 0 2px rgba(220, 20, 60, 0.2);
}

/* Button styles */
.auth-button {
  background: var(--auth-button-bg);
  color: white;
  transition: all 0.3s ease;
}

.auth-button:hover {
  background: var(--auth-button-hover);
  transform: translateY(-1px);
}

/* Text styles */
.auth-text-primary {
  color: var(--auth-text-primary);
}

.auth-text-secondary {
  color: var(--auth-text-secondary);
}

.auth-text-muted {
  color: var(--auth-text-muted);
}

.auth-text-accent {
  color: var(--auth-accent);
}

/* Link styles */
.auth-link {
  color: var(--auth-accent);
  transition: all 0.2s ease;
}

.auth-link:hover {
  color: var(--auth-accent-hover);
  text-decoration: underline;
}
