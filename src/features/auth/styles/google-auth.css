/* Google Authentication Styles */

/* Google button container */
.google-button-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
  width: 100%;
  min-height: 48px; /* Ensure consistent height during loading */
}

/* Divider styles */
.auth-divider {
  margin: 15px 0;
  text-align: center;
  position: relative;
}

.auth-divider:before,
.auth-divider:after {
  content: "";
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background-color: #ddd;
}

.auth-divider:before { left: 0; }
.auth-divider:after { right: 0; }

/* Dark mode adjustments */
.dark .auth-divider:before,
.dark .auth-divider:after {
  background-color: #444;
}

/* Google button wrapper */
.google-auth-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

/* Custom Google button */
.google-auth-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: white;
  color: #444;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
}

.google-auth-button:hover {
  background-color: #f8f8f8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.google-auth-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.4);
}

/* Google icon */
.google-icon {
  width: 18px;
  height: 18px;
}

/* Loading state */
.google-auth-loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Loading spinner container */
.google-button-container .flex {
  min-height: 48px;
  padding: 10px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Loading text */
.google-button-container .text-muted-foreground {
  color: #666;
  margin-top: 8px;
}

/* Error message */
.google-auth-error {
  color: #d32f2f;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}
