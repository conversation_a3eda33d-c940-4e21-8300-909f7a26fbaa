/* Modern authentication styles */

.auth-container {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.auth-container:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.login-background {
  background-image: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nepali-gradient {
  background: linear-gradient(135deg, #DC143C 0%, #8B0000 100%);
  transition: all 0.3s ease;
}

.nepali-gradient:hover {
  background: linear-gradient(135deg, #e82c52 0%, #a50000 100%);
  transform: translateY(-1px);
}

.form-input {
  transition: all 0.2s ease;
  border-width: 1px;
}

.form-input:focus {
  border-color: #DC143C;
  box-shadow: 0 0 0 2px rgba(220, 20, 60, 0.2);
}

.form-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.form-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
}

.form-button:hover::before {
  left: 100%;
}

.auth-card {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
}

.auth-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(220, 20, 60, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
}

.auth-card:hover::before {
  opacity: 1;
}

/* Dark mode specific styles */
.dark .form-input {
  background-color: rgba(30, 30, 30, 0.8);
  border-color: rgba(75, 75, 75, 0.5);
  color: #f0f0f0;
}

.dark .form-input:focus {
  border-color: #ff6b81;
  box-shadow: 0 0 0 2px rgba(255, 107, 129, 0.2);
}

/* Animation classes */
.slide-in {
  animation: slideIn 0.4s ease forwards;
}

.slide-out {
  animation: slideOut 0.4s ease forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-50px);
  }
}

/* Floating animation for decorative elements */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Pulse animation for buttons */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 20, 60, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 20, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 20, 60, 0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .auth-container {
    border-radius: 0.5rem;
  }
}
