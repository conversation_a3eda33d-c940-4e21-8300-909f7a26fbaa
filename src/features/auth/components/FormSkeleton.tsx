import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { useTheme } from "@/core/hooks/useTheme";
import "../styles/auth.css";
import "../styles/theme.css";

interface FormSkeletonProps {
  type: 'login' | 'signup';
  className?: string;
}

/**
 * Skeleton loading component for auth forms
 */
const FormSkeleton: React.FC<FormSkeletonProps> = ({ type, className }) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  return (
    <div className={cn("space-y-4 w-full", className)}>
      {/* Username field */}
      <div className="space-y-2">
        <Skeleton className={cn(
          "h-10 w-full rounded-md",
          isDarkMode ? "bg-gray-700" : "bg-gray-200 auth-input"
        )} />
      </div>

      {/* Email field - only for signup */}
      {type === 'signup' && (
        <div className="space-y-2">
          <Skeleton className={cn(
            "h-10 w-full rounded-md",
            isDarkMode ? "bg-gray-700" : "bg-gray-200"
          )} />
        </div>
      )}

      {/* Password field */}
      <div className="space-y-2">
        <Skeleton className={cn(
          "h-10 w-full rounded-md",
          isDarkMode ? "bg-gray-700" : "bg-gray-200"
        )} />
      </div>

      {/* Confirm Password field - only for signup */}
      {type === 'signup' && (
        <div className="space-y-2">
          <Skeleton className={cn(
            "h-10 w-full rounded-md",
            isDarkMode ? "bg-gray-700" : "bg-gray-200"
          )} />
        </div>
      )}

      {/* Full Name field - only for signup */}
      {type === 'signup' && (
        <div className="space-y-2">
          <Skeleton className={cn(
            "h-10 w-full rounded-md",
            isDarkMode ? "bg-gray-700" : "bg-gray-200"
          )} />
        </div>
      )}

      {/* Submit button */}
      <Skeleton className={cn(
        "h-10 w-full rounded-md",
        isDarkMode ? "bg-gray-700" : "bg-gray-200 auth-button"
      )} />

      {/* Separator */}
      <div className="relative my-4">
        <Skeleton className={cn(
          "h-[1px] w-full",
          isDarkMode ? "bg-gray-700" : "bg-gray-200"
        )} />
      </div>

      {/* Google button */}
      <Skeleton className={cn(
        "h-10 w-full rounded-md",
        isDarkMode ? "bg-gray-700" : "bg-gray-200 auth-button"
      )} />
    </div>
  );
};

export default FormSkeleton;
