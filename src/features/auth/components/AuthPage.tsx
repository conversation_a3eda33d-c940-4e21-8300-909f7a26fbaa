import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "@/context/AuthContext";
import { useTheme } from "@/core/hooks/useTheme";
import { validateTenant } from "@/api/authService";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import LoginForm from "./LoginForm";
import SignupForm from "./SignupForm";
import FormSkeleton from "./FormSkeleton";
import "../styles/auth.css";
import "../styles/theme.css";

interface AuthPageProps {
  initialSlug?: string;
  initialMode?: 'login' | 'signup';
}

/**
 * Modern single-page authentication component with animated transitions
 * Handles both login and signup functionality
 */
const AuthPage: React.FC<AuthPageProps> = ({ initialSlug = "", initialMode = "login" }) => {
  const { isAuthenticated, setTenantSlug: updateTenantSlug } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // State for tenant verification
  const [tenantSlug, setTenantSlug] = useState(initialSlug || localStorage.getItem("nepali_app_client") || "");
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(tenantSlug ? 1 : 0);

  // State for form mode (login or signup)
  const [activeView, setActiveView] = useState<'login' | 'signup'>(initialMode);
  // State for transition loading
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Redirect if already authenticated (but not during signup flow)
  useEffect(() => {
    if (isAuthenticated) {
      const isSignupFlow = localStorage.getItem("signup_flow");
      if (!isSignupFlow) {
        navigate("/dashboard");
      }
    }
  }, [isAuthenticated, navigate]);

  // Set tenant slug if provided
  useEffect(() => {
    if (initialSlug) {
      setTenantSlug(initialSlug);
      setStep(1);
    }
  }, [initialSlug]);

  // Handle tenant verification
  const handleTenantSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tenantSlug.trim()) {
      toast({
        title: "Error",
        description: "Please enter a tenant ID",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    const isValid = await validateTenant(tenantSlug);

    setIsLoading(false);

    if (isValid) {
      updateTenantSlug(tenantSlug);
      setStep(1);
    } else {
      toast({
        title: "Error",
        description: "Invalid tenant ID. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Toggle between login and signup views with transition effect
  const toggleView = () => {
    setIsTransitioning(true);

    // Short delay to show skeleton before switching views
    setTimeout(() => {
      setActiveView(prev => prev === 'login' ? 'signup' : 'login');

      // Short delay to hide skeleton after switching views
      setTimeout(() => {
        setIsTransitioning(false);
      }, 300);
    }, 300);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-md mx-auto"
    >
      <Card className={cn(
        "w-full overflow-hidden auth-card",
        isDarkMode ? "auth-dark-theme" : "auth-light-theme"
      )}>
        <CardHeader>
          <CardTitle className={cn(
            "text-2xl text-center",
            isDarkMode ? "auth-text-accent" : "text-nepali-maroon"
          )}>
            {step === 0
              ? "Enter Tenant ID"
              : activeView === 'login'
                ? "Welcome Back"
                : "Create Your Account"}
          </CardTitle>
          <CardDescription className={cn(
            "text-center",
            isDarkMode ? "auth-text-secondary" : "text-gray-500"
          )}>
            {step === 0
              ? "Please enter your organization's tenant ID"
              : activeView === 'login'
                ? `Sign in to ${tenantSlug}`
                : `Sign up for ${tenantSlug}`}
          </CardDescription>
        </CardHeader>
        <CardContent className="overflow-hidden">
          {step === 0 ? (
            <form onSubmit={handleTenantSubmit} className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder="Tenant ID"
                  value={tenantSlug}
                  onChange={(e) => setTenantSlug(e.target.value)}
                  required
                  className="w-full form-input auth-input"
                />
              </div>
              <Button
                type="submit"
                className="w-full auth-button form-button pulse-animation"
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Continue"}
              </Button>
            </form>
          ) : (
            <div className="relative">
              <AnimatePresence mode="wait" initial={false}>
                {isTransitioning ? (
                  <motion.div
                    key="skeleton"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="w-full"
                  >
                    <FormSkeleton type={activeView} />
                  </motion.div>
                ) : activeView === 'login' ? (
                  <motion.div
                    key="login"
                    initial={{ x: 300, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    exit={{ x: -300, opacity: 0 }}
                    transition={{ duration: 0.4, ease: "easeInOut" }}
                    className="w-full"
                  >
                    <LoginForm tenantSlug={tenantSlug} />
                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Don't have an account?{" "}
                        <button
                          type="button"
                          onClick={toggleView}
                          className="text-sm font-medium hover:underline auth-link"
                        >
                          Sign up
                        </button>
                      </p>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="signup"
                    initial={{ x: -300, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    exit={{ x: 300, opacity: 0 }}
                    transition={{ duration: 0.4, ease: "easeInOut" }}
                    className="w-full"
                  >
                    <SignupForm tenantSlug={tenantSlug} />
                    <div className="mt-4 text-center">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Already have an account?{" "}
                        <button
                          type="button"
                          onClick={toggleView}
                          className="text-sm font-medium hover:underline auth-link"
                        >
                          Sign in
                        </button>
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AuthPage;
