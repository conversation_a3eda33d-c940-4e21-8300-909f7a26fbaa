import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTheme } from "@/core/hooks/useTheme";
import { cn } from "@/lib/utils";
import { loginUser } from "@/api/authService";
import GoogleAuth from "./GoogleAuth";
import "../styles/auth.css";
import "../styles/theme.css";

interface LoginFormProps {
  tenantSlug: string;
}

/**
 * Login form component with email/password and Google authentication
 */
const LoginForm: React.FC<LoginFormProps> = ({ tenantSlug }) => {
  const { login } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Form state
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [errors, setErrors] = useState<{
    username?: string;
    password?: string;
  }>({});

  // Handle login form submission
  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset errors
    setErrors({});

    // Validate form
    let hasErrors = false;
    const newErrors: { username?: string; password?: string } = {};

    if (!username.trim()) {
      newErrors.username = "Username is required";
      hasErrors = true;
    }

    if (!password.trim()) {
      newErrors.password = "Password is required";
      hasErrors = true;
    }

    if (hasErrors) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);

    const response = await loginUser(username, password, tenantSlug);

    setIsLoading(false);

    if (response.success && response.user) {
      // Clear any signup flow flag since this is a login
      localStorage.removeItem("signup_flow");

      login(response.user);
      navigate("/dashboard");
    } else {
      toast({
        title: "Login Failed",
        description: response.message || "Invalid username or password",
        variant: "destructive",
      });
    }
  };

  // Google sign-in is handled by the GoogleAuth component

  return (
    <form onSubmit={handleLoginSubmit} className="space-y-4">
      <div className="space-y-2">
        <Input
          type="text"
          placeholder="Username or Email"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
          className={cn("w-full form-input auth-input", errors.username && "border-red-500")}
        />
        {errors.username && (
          <p className="text-red-500 text-xs mt-1">{errors.username}</p>
        )}
      </div>

      <div className="space-y-2">
        <Input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className={cn("w-full form-input auth-input", errors.password && "border-red-500")}
        />
        {errors.password && (
          <p className="text-red-500 text-xs mt-1">{errors.password}</p>
        )}
      </div>

      <Button
        type="submit"
        className="w-full auth-button form-button"
        disabled={isLoading}
      >
        {isLoading ? "Signing in..." : "Sign in"}
      </Button>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className={cn(
            "px-2",
            isDarkMode ? "bg-gray-800" : "bg-white"
          )}>
            Or continue with
          </span>
        </div>
      </div>

      <GoogleAuth tenantSlug={tenantSlug} />
    </form>
  );
};

export default LoginForm;
