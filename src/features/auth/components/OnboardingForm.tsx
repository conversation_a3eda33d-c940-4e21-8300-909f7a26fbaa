import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { useTheme } from "@/core/hooks/useTheme";
import { cn } from "@/lib/utils";
import { submitOnboarding } from "@/api/authService";
import { OnboardingData } from "@/types/User";
import { motion } from "framer-motion";

/**
 * Onboarding form component for new users
 */
const OnboardingForm: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Form state
  const [age, setAge] = useState<number>(7);
  const [difficultyLevel, setDifficultyLevel] = useState<"easy" | "medium" | "hard">("easy");
  const [preferredTopics, setPreferredTopics] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const [errors, setErrors] = useState<{
    age?: string;
    difficultyLevel?: string;
  }>({});

  // Clear signup flow flag when component mounts
  useEffect(() => {
    localStorage.removeItem("signup_flow");
  }, []);

  // Available topics
  const availableTopics = [
    "math",
    "science",
    "reading",
    "writing",
    "history",
    "geography",
    "art",
    "music",
    "sports",
    "animals",
    "nature",
    "technology"
  ];

  // Validate form
  const validateForm = () => {
    const newErrors: {
      age?: string;
      difficultyLevel?: string;
    } = {};
    let isValid = true;

    if (!age || age < 3 || age > 18) {
      newErrors.age = "Age must be between 3 and 18";
      isValid = false;
    }

    if (!difficultyLevel) {
      newErrors.difficultyLevel = "Please select a difficulty level";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle topic selection
  const handleTopicChange = (topic: string, checked: boolean) => {
    if (checked) {
      setPreferredTopics(prev => [...prev, topic]);
    } else {
      setPreferredTopics(prev => prev.filter(t => t !== topic));
    }
  };

  // Convert difficulty level string to number for API
  const getDifficultyLevelNumber = (level: "easy" | "medium" | "hard"): number => {
    const mapping = {
      "easy": 1,
      "medium": 2,
      "hard": 3
    };
    return mapping[level];
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    const onboardingData: OnboardingData = {
      age,
      difficulty_level: getDifficultyLevelNumber(difficultyLevel) as number,
      preferred_topics: preferredTopics.length > 0 ? preferredTopics : undefined
    };

    const response = await submitOnboarding(onboardingData);
    setIsLoading(false);

    if (response.success) {
      toast({
        title: "Welcome!",
        description: "Your profile has been set up successfully. Let's start learning!",
      });

      // Redirect to dashboard
      navigate("/dashboard");
    } else {
      toast({
        title: "Setup Failed",
        description: response.message || "An error occurred during setup",
        variant: "destructive",
      });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full max-w-2xl mx-auto"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Age Input */}
        <div className="space-y-2">
          <Label htmlFor="age" className={cn(
            "text-sm font-medium",
            isDarkMode ? "text-gray-200" : "text-gray-700"
          )}>
            How old are you?
          </Label>
          <Input
            id="age"
            type="number"
            min="3"
            max="18"
            value={age}
            onChange={(e) => setAge(parseInt(e.target.value) || 7)}
            required
            className={cn("w-full", errors.age && "border-red-500")}
          />
          {errors.age && (
            <p className="text-red-500 text-xs mt-1">{errors.age}</p>
          )}
        </div>

        {/* Difficulty Level */}
        <div className="space-y-2">
          <Label className={cn(
            "text-sm font-medium",
            isDarkMode ? "text-gray-200" : "text-gray-700"
          )}>
            What difficulty level would you like to start with?
          </Label>
          <Select value={difficultyLevel} onValueChange={(value: "easy" | "medium" | "hard") => setDifficultyLevel(value)}>
            <SelectTrigger className={cn("w-full", errors.difficultyLevel && "border-red-500")}>
              <SelectValue placeholder="Select difficulty level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="easy">Easy - Perfect for beginners</SelectItem>
              <SelectItem value="medium">Medium - Some challenge</SelectItem>
              <SelectItem value="hard">Hard - For advanced learners</SelectItem>
            </SelectContent>
          </Select>
          {errors.difficultyLevel && (
            <p className="text-red-500 text-xs mt-1">{errors.difficultyLevel}</p>
          )}
        </div>

        {/* Preferred Topics */}
        <div className="space-y-3">
          <Label className={cn(
            "text-sm font-medium",
            isDarkMode ? "text-gray-200" : "text-gray-700"
          )}>
            What topics interest you? (Optional)
          </Label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {availableTopics.map((topic) => (
              <div key={topic} className="flex items-center space-x-2">
                <Checkbox
                  id={topic}
                  checked={preferredTopics.includes(topic)}
                  onCheckedChange={(checked) => handleTopicChange(topic, checked as boolean)}
                />
                <Label
                  htmlFor={topic}
                  className={cn(
                    "text-sm capitalize cursor-pointer",
                    isDarkMode ? "text-gray-300" : "text-gray-600"
                  )}
                >
                  {topic}
                </Label>
              </div>
            ))}
          </div>
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? "Setting up your profile..." : "Complete Setup"}
        </Button>
      </form>
    </motion.div>
  );
};

export default OnboardingForm;
