import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Phone, Mail, Lock, Check, ExternalLink } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { Badge } from '@/components/ui/badge';

const UserProfilePopover = () => {
  const { user } = useAuth();

  return (
    <div className="py-2">
      {/* User Info Header */}
      <div className="px-4 mb-2">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={user?.profile_picture} alt={user?.full_name || user?.username} />
            <AvatarFallback>
              {(user?.full_name || user?.username)?.split(' ').map(part => part[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-semibold">{user?.full_name}</h3>
              <Badge variant="secondary" className="text-xs">admin</Badge>
            </div>
            <p className="text-sm text-muted-foreground">@{user?.username}</p>
          </div>
        </div>
      </div>

      <Separator className="my-2" />

      {/* Contact Information */}
      <div className="px-4">
        <div className="space-y-2">
          {/* Email Section */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail className="size-4 text-muted-foreground" />
              <span className="text-sm">{user?.email || 'Add email'}</span>
            </div>
            {user?.email ? (
              <Check className="size-4 text-green-500" />
            ) : (
              <Button variant="ghost" size="sm" className="text-xs">
                <ExternalLink className="size-3 mr-1" />
                Link
              </Button>
            )}
          </div>

          {/* Phone Section */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Phone className="size-4 text-muted-foreground" />
              <span className="text-sm">
                {user && user.phone ? (
                  <>
                    <span className="mr-1">🇳🇵</span>
                    {user.phone}
                  </>
                ) : (
                  'Add phone number'
                )}
              </span>
            </div>
            {user && user.phone ? (
              <Check className="size-4 text-green-500" />
            ) : (
              <Button variant="ghost" size="sm" className="text-xs">
                <ExternalLink className="size-3 mr-1" />
                Link
              </Button>
            )}
          </div>
        </div>
      </div>

      <Separator className="my-2" />

      {/* Actions */}
      <div className="px-4 pb-2">
        <Button variant="outline" className="w-full" size="sm">
          <Lock className="size-4 mr-2" />
          Change Password
        </Button>
      </div>
    </div>
  );
};

export default UserProfilePopover;
