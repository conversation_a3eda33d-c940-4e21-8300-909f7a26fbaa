import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { GoogleLogin } from '@react-oauth/google';
import axios from 'axios';
import { useAuth } from '@/context/AuthContext';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { jwtDecode } from 'jwt-decode';
// import UserProfilePreview from './UserProfilePreview';
import UserProfilePopover from './UserProfilePopover';
import '../styles/google-auth.css';
import { ImageOff } from 'lucide-react';

interface GoogleAuthProps {
  tenantSlug: string;
}

/**
 * GoogleAuth component that uses the @react-oauth/google library
 * This component implements Google authentication using the direct token method
 * with the GoogleLogin component from @react-oauth/google
 */
const GoogleAuth: React.FC<GoogleAuthProps> = ({ tenantSlug }) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const API_URL = import.meta.env.VITE_API_URL || '';

  // Handle Google sign-in success
  const handleGoogleSuccess = async (credentialResponse: any) => {
    try {
      setIsLoading(true);
      console.log('Google credential received:', credentialResponse);

      // Log the credential details for debugging
      if (credentialResponse.credential) {
        console.log('Credential length:', credentialResponse.credential.length);
        console.log('Credential first 20 chars:', credentialResponse.credential.substring(0, 20) + '...');
      } else {
        console.error('No credential found in the response');
      }

      console.log('Tenant slug:', tenantSlug);
      console.log('API URL:', API_URL);

      // Prepare user information to send to the backend
      let userInfo = {};
      try {
        const decodedToken: any = jwtDecode(credentialResponse.credential);
        userInfo = {
          email: decodedToken.email,
          name: decodedToken.name,
          given_name: decodedToken.given_name,
          family_name: decodedToken.family_name,
          picture: decodedToken.picture,
          sub: decodedToken.sub,
        };
        console.log('Extracted user info:', userInfo);
      } catch (error) {
        console.error('Error extracting user info from token:', error);
      }

      // Send the ID token and user info to your backend
      const requestData = {
        id_token: credentialResponse.credential,
        client_id: tenantSlug,
      };

      console.log('Sending request to backend:', {
        ...requestData,
      });

      const response = await axios.post(`${API_URL}/auth/google-auth`, requestData, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*'
        }
      });

      console.log('Backend response:', response);

      // Handle successful login
      if (response.data) {
        // Transform backend response to match UserData type if needed
        const userData = {
          id: response.data.id,
          username: response.data.username,
          email: response.data.email || response.data.username,
          role: response.data.role,
          token: response.data.access_token,
          tenantSlug: response.data.tenant_slug,
          tenantId: response.data.tenant_id,
          tenantLabel: response.data.tenant_label,
          tokenType: response.data.token_type,
          virtueProjectNameId: response.data.tenant_id,
          full_name: response.data.full_name
        };

        // Login the user
        login(userData);

        // Show profile preview instead of immediate redirect
        setShowProfile(true);

        // Redirect to dashboard after a delay (optional)
        // setTimeout(() => {
        //   navigate('/dashboard');
        // }, 3000);
      }
    } catch (error: any) {
      console.error('Error handling Google sign-in:', error);

      // Extract detailed error information
      let errorMessage = 'Failed to authenticate with Google. Please try again.';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);

        if (error.response.data && error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data && typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        }
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
        errorMessage = 'No response received from server. Please check your connection.';
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);
        errorMessage = error.message || errorMessage;
      }

      toast({
        title: 'Authentication Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };



  // Get Google client ID from environment variables
  const googleClientId = import.meta.env.VITE_GOOGLE_CLIENT_ID || '';

  // Log Google client ID for debugging
  console.log('Component Google Client ID:', googleClientId);

  // If user is logged in and showProfile is true, show the profile preview
  if (showProfile && user) {
    return (
      <div className="w-full max-w-md mx-auto">
        <UserProfilePopover />
      </div>
    );
  }

  return (
    <div className="google-button-container">
      {isLoading ? (
        <div className="flex flex-col items-center space-y-2">
          <LoadingSpinner size="md" />
          <p className="text-sm text-muted-foreground">Authenticating with Google...</p>
        </div>
      ) : googleClientId ? (
        <GoogleLogin
          onSuccess={handleGoogleSuccess}
          onError={() => {
            // Log the error for debugging
            console.error('Google sign-in error occurred');
            toast({
              title: 'Error',
              description: 'Google sign-in failed. Please try again.',
              variant: 'destructive',
            });
          }}
          useOneTap
          theme="outline"
          size="large"
          text="signin_with"
          shape="rectangular"
          width="300px"
        />
      ) : (
        <div className="flex flex-col items-center space-y-2">
          <p className="text-sm text-red-500">Google client ID not configured. Please check your environment variables.</p>
        </div>
      )}
    </div>
  );
};

export default GoogleAuth;
