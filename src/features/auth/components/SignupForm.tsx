import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useTheme } from "@/core/hooks/useTheme";
import { cn } from "@/lib/utils";
import { signupUser } from "@/api/authService";
import { SignupData } from "@/types/User";
import { useAuth } from "@/context/AuthContext";
import GoogleAuth from "./GoogleAuth";
import "../styles/auth.css";
import "../styles/theme.css";

interface SignupFormProps {
  tenantSlug: string;
}

/**
 * Signup form component with email/password and Google authentication
 */
const SignupForm: React.FC<SignupFormProps> = ({ tenantSlug }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const { login } = useAuth();
  const isDarkMode = theme === 'dark';

  // Form state
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [fullName, setFullName] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [errors, setErrors] = useState<{
    username?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
  }>({});

  // Validate form
  const validateForm = () => {
    const newErrors: {
      username?: string;
      email?: string;
      password?: string;
      confirmPassword?: string;
    } = {};
    let isValid = true;

    if (!username.trim()) {
      newErrors.username = "Username is required";
      isValid = false;
    } else if (username.length < 3) {
      newErrors.username = "Username must be at least 3 characters";
      isValid = false;
    }

    if (!email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Email is invalid";
      isValid = false;
    }

    if (!password.trim()) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (password.length < 6) {
      newErrors.password = "Password must be at least 6 characters";
      isValid = false;
    }

    if (password !== confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle signup form submission
  const handleSignupSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    const signupData: SignupData = {
      username,
      email,
      password,
      client_id: tenantSlug,
    };

    if (fullName.trim()) {
      signupData.full_name = fullName;
    }

    const response = await signupUser(signupData);
    setIsLoading(false);

    if (response.success) {
      // Check if user data is returned (auto-login)
      if (response.user) {
        // Auto-login the user
        login(response.user);

        toast({
          title: "Welcome!",
          description: "Account created successfully! Let's set up your profile.",
        });

        // Set a flag to indicate this is a signup flow
        localStorage.setItem("signup_flow", "true");

        // Redirect to onboarding with replace to prevent back navigation issues
        setTimeout(() => {
          navigate("/onboarding", { replace: true });
        }, 100); // Small delay to ensure login state is updated
      } else {
        // Fallback to login page if no user data returned
        toast({
          title: "Success",
          description: "Signup successful! Please login to continue.",
        });

        // Clear form and switch to login view
        setUsername("");
        setEmail("");
        setPassword("");
        setConfirmPassword("");
        setFullName("");

        // Redirect to login page with the same tenant
        navigate(`/${tenantSlug}/login`);
      }
    } else {
      toast({
        title: "Signup Failed",
        description: response.message || "An error occurred during signup",
        variant: "destructive",
      });
    }
  };

  // Google sign-in is handled by the GoogleAuth component

  return (
    <form onSubmit={handleSignupSubmit} className="space-y-4">
      <div className="space-y-2">
        <Input
          type="text"
          placeholder="Username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
          className={cn("w-full form-input auth-input", errors.username && "border-red-500")}
        />
        {errors.username && (
          <p className="text-red-500 text-xs mt-1">{errors.username}</p>
        )}
      </div>

      <div className="space-y-2">
        <Input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className={cn("w-full form-input auth-input", errors.email && "border-red-500")}
        />
        {errors.email && (
          <p className="text-red-500 text-xs mt-1">{errors.email}</p>
        )}
      </div>

      <div className="space-y-2">
        <Input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className={cn("w-full form-input auth-input", errors.password && "border-red-500")}
        />
        {errors.password && (
          <p className="text-red-500 text-xs mt-1">{errors.password}</p>
        )}
      </div>

      <div className="space-y-2">
        <Input
          type="password"
          placeholder="Confirm Password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          required
          className={cn("w-full form-input auth-input", errors.confirmPassword && "border-red-500")}
        />
        {errors.confirmPassword && (
          <p className="text-red-500 text-xs mt-1">{errors.confirmPassword}</p>
        )}
      </div>

      <div className="space-y-2">
        <Input
          type="text"
          placeholder="Full Name (Optional)"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
          className="w-full form-input auth-input"
        />
      </div>

      <Button
        type="submit"
        className="w-full auth-button form-button"
        disabled={isLoading}
      >
        {isLoading ? "Signing up..." : "Sign up"}
      </Button>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className={cn(
            "px-2",
            isDarkMode ? "bg-gray-800" : "bg-white"
          )}>
            Or continue with
          </span>
        </div>
      </div>

      <GoogleAuth tenantSlug={tenantSlug} />
    </form>
  );
};

export default SignupForm;
