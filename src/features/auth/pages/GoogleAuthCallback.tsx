import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { handleGoogleCallback } from "@/api/authService";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import LoadingSpinner from "@/components/common/LoadingSpinner";
import { Button } from "@/components/ui/button";
import axios from "axios";

/**
 * Google OAuth callback handler component
 * This component is kept for backward compatibility with any existing links
 * but the main authentication now uses the direct token method with GoogleLogin
 */
const GoogleAuthCallback: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [manualRetryCount, setManualRetryCount] = useState(0);
  const API_URL = import.meta.env.VITE_API_URL || '';

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('GoogleAuthCallback: Handling callback');
        console.log('URL search params:', location.search);

        // Get the parameters from the URL
        const urlParams = new URLSearchParams(location.search);
        const code = urlParams.get("code");
        const credential = urlParams.get("credential");
        const state = urlParams.get("state");
        const token = urlParams.get("token"); // Some implementations might return token directly

        console.log('Extracted params:', {
          code: code ? 'present' : 'not present',
          credential: credential ? 'present' : 'not present',
          state: state ? 'present' : 'not present',
          token: token ? 'present' : 'not present'
        });

        // Get the tenant slug from localStorage or state
        let tenantSlug = localStorage.getItem("nepali_app_client");

        if (state) {
          try {
            const stateData = JSON.parse(atob(state));
            if (stateData.tenantSlug) {
              tenantSlug = stateData.tenantSlug;
            }
          } catch (e) {
            console.error("Error parsing state:", e);
          }
        }

        if (!tenantSlug) {
          setError("No tenant ID found. Please try again.");
          setIsLoading(false);
          return;
        }

        // If token is directly provided in the URL (some implementations do this)
        if (token) {
          try {
            // Verify the token with your backend
            const response = await axios.post(`${API_URL}/auth/verify-token`, {
              token,
              client_id: tenantSlug
            });

            if (response.data) {
              // Transform backend response to match UserData type if needed
              const userData = {
                id: response.data.id,
                username: response.data.username,
                email: response.data.email || response.data.username,
                role: response.data.role,
                token: token,
                tenantSlug: response.data.tenant_slug || tenantSlug,
                tenantId: response.data.tenant_id,
                tenantLabel: response.data.tenant_label,
                tokenType: response.data.token_type || 'Bearer',
                virtueProjectNameId: response.data.tenant_id,
                full_name: response.data.full_name
              };

              // Login the user
              login(userData);
              navigate("/dashboard");
              return;
            }
          } catch (error) {
            console.error("Error verifying token:", error);
            // Continue with other authentication methods
          }
        }

        let response;

        // Handle different authentication flows
        if (credential) {
          console.log('Using credential flow with ID token');
          // New Google Identity Services flow - using ID token
          response = await handleGoogleCallback(credential, tenantSlug, true);
        } else if (code) {
          console.log('Using authorization code flow');
          // Legacy OAuth flow - using authorization code
          response = await handleGoogleCallback(code, tenantSlug, false);
        } else {
          console.error('No credential or code found in URL');
          setError("No authentication data found in the callback URL");
          setIsLoading(false);
          return;
        }

        if (response.success && response.user) {
          // Login the user
          login(response.user);

          // Check if this is a signup flow or login flow
          const isSignupFlow = localStorage.getItem("signup_flow");
          if (isSignupFlow) {
            // Clear the flag and redirect to onboarding
            localStorage.removeItem("signup_flow");
            navigate("/onboarding");
          } else {
            // Regular login, redirect to dashboard
            navigate("/dashboard");
          }
        } else {
          setError(response.message || "An error occurred during Google authentication");
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Error handling Google callback:", error);
        setError("An unexpected error occurred. Please try again.");
        setIsLoading(false);
      }
    };

    handleCallback();
  }, [location, login, navigate, manualRetryCount, API_URL]);

  const handleManualRetry = () => {
    setIsLoading(true);
    setError(null);
    setManualRetryCount(prev => prev + 1);
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-center">
              {error ? "Authentication Error" : "Authenticating..."}
            </CardTitle>
            <p className="text-center text-sm text-muted-foreground mt-2">
              Note: We've updated our authentication system. Please use the direct Google sign-in button on the login page for a better experience.
            </p>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center p-6">
            {isLoading ? (
              <div className="flex flex-col items-center space-y-4">
                <LoadingSpinner size="lg" />
                <p className="text-center text-gray-600 dark:text-gray-400">
                  Please wait while we authenticate you with Google...
                </p>
              </div>
            ) : (
              <div className="text-center">
                <div className="mb-4 text-red-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 mx-auto"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <p className="text-red-500 mb-4">{error}</p>
                <div className="flex flex-col space-y-2">
                  <Button
                    onClick={handleManualRetry}
                    variant="default"
                    className="w-full mb-2"
                  >
                    Retry Authentication
                  </Button>
                  <Button
                    onClick={() => navigate("/")}
                    variant="outline"
                    className="w-full"
                  >
                    Return to Login
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default GoogleAuthCallback;
