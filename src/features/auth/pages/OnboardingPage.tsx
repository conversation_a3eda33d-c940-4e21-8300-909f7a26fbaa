import React from "react";
import { motion } from "framer-motion";
import { useTheme } from "@/core/hooks/useTheme";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import OnboardingForm from "../components/OnboardingForm";

/**
 * Onboarding page for new users after signup
 */
const OnboardingPage: React.FC = () => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  return (
    <div className={cn(
      "min-h-screen flex items-center justify-center p-4",
      isDarkMode ? "bg-gray-900" : "bg-gradient-to-br from-blue-50 to-indigo-100"
    )}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl"
      >
        <Card className={cn(
          "w-full shadow-xl",
          isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
        )}>
          <CardHeader className="text-center pb-6">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <CardTitle className={cn(
                "text-3xl font-bold mb-2",
                isDarkMode ? "text-white" : "text-gray-900"
              )}>
                Welcome to Audio Quiz Genie! 🎉
              </CardTitle>
              <CardDescription className={cn(
                "text-lg",
                isDarkMode ? "text-gray-300" : "text-gray-600"
              )}>
                Let's set up your learning profile to personalize your experience
              </CardDescription>
            </motion.div>
          </CardHeader>
          <CardContent className="px-6 pb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              <OnboardingForm />
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};

export default OnboardingPage;
