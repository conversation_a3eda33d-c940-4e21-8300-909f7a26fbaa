import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import AuthPage from "@/features/auth/components/AuthPage";
import { motion } from "framer-motion";
import { useTheme } from "@/core/hooks/useTheme";

/**
 * Enhanced landing page with animations and theme support
 */
const LandingPage: React.FC = () => {
  const { tenantSlug, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { slug } = useParams<{ slug?: string }>();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  useEffect(() => {
    if (isAuthenticated) {
      const isSignupFlow = localStorage.getItem("signup_flow");
      if (!isSignupFlow) {
        navigate("/dashboard");
      }
    }
  }, [isAuthenticated, navigate]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  const logoVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 20,
        duration: 0.8
      }
    },
    float: {
      y: [0, -15, 0],
      transition: {
        y: {
          repeat: Infinity,
          duration: 3,
          ease: "easeInOut"
        }
      }
    }
  };

  return (
    <div className={`min-h-screen flex flex-col md:flex-row ${isDarkMode ? 'bg-gray-900' : ''}`}>
      {/* Left side - Login Form */}
      <motion.div
        className="w-full md:w-1/3 flex items-center justify-center p-4 md:p-8 login-background"
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthPage initialSlug={slug || tenantSlug || ""} initialMode="login" />
      </motion.div>

      {/* Right side - App Showcase */}
      <motion.div
        className="w-full md:w-2/3 bg-gradient-to-br from-nepali-red to-nepali-orange p-8 flex flex-col justify-center items-center text-white"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <motion.div
          className="max-w-md mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-6"
            variants={itemVariants}
          >
            Learn Fluent Nepali
          </motion.h1>

          <motion.div
            className="mb-8"
            variants={logoVariants}
            initial="hidden"
            animate={["visible", "float"]}
          >
            <svg className="w-32 h-32 mx-auto opacity-90" viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M250 500L0 250L250 0L500 250L250 500Z" fill="white" fillOpacity="0.9" />
              <path d="M250 450L50 250L250 50L450 250L250 450Z" fill="#003893" />
              <path d="M250 400L100 250L250 100L400 250L250 400Z" fill="#DC143C" />
            </svg>
          </motion.div>

          <motion.p
            className="text-xl mb-6"
            variants={itemVariants}
          >
            Master Nepali language through interactive lessons, quizzes, and real-time practice.
          </motion.p>

          <motion.div
            className="grid grid-cols-2 gap-4 text-center"
            variants={containerVariants}
          >
            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Interactive Lessons</h3>
              <p className="text-sm mt-2">Learn with engaging, adaptive content</p>
            </motion.div>

            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Speech Recognition</h3>
              <p className="text-sm mt-2">Practice pronunciation with feedback</p>
            </motion.div>

            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Cultural Insights</h3>
              <p className="text-sm mt-2">Understand Nepali culture and customs</p>
            </motion.div>

            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Progress Tracking</h3>
              <p className="text-sm mt-2">Monitor your learning journey</p>
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default LandingPage;
