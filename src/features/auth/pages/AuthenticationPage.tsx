import React, { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { motion } from "framer-motion";
import { useTheme } from "@/core/hooks/useTheme";
import AuthPage from "@/features/auth/components/AuthPage";
import "@/features/auth/styles/auth.css";
import "@/features/auth/styles/theme.css";

interface AuthenticationPageProps {
  mode?: 'login' | 'signup';
}

/**
 * Unified authentication page that handles both login and signup
 * Uses the new AuthPage component with animated transitions
 */
const AuthenticationPage: React.FC<AuthenticationPageProps> = ({ mode = 'login' }) => {
  const { slug } = useParams<{ slug: string }>();
  const { isAuthenticated, setTenantSlug } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  useEffect(() => {
    if (slug) {
      setTenantSlug(slug);
    }

    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [slug, isAuthenticated, navigate, setTenantSlug]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const logoVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.6 }
    },
    float: {
      y: [0, -10, 0],
      transition: {
        duration: 3,
        repeat: Infinity,
        repeatType: "reverse" as const
      }
    }
  };

  return (
    <div className={`min-h-screen flex flex-col md:flex-row ${isDarkMode ? 'auth-dark-theme auth-pattern' : 'auth-light-theme auth-pattern'}`}>
      {/* Left side - Auth Form */}
      <motion.div
        className="w-full md:w-1/3 flex items-center justify-center p-4 md:p-8 login-background"
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AuthPage initialSlug={slug || ""} initialMode={mode} />
      </motion.div>

      {/* Right side - Decorative content */}
      <motion.div
        className="hidden md:flex md:w-2/3 nepali-gradient-mixed p-8 text-white flex-col items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <motion.div
          className="max-w-2xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div
            className="mb-8"
            variants={logoVariants}
            initial="hidden"
            animate={["visible", "float"]}
          >
            <svg className="w-32 h-32 mx-auto opacity-90" viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M250 500L0 250L250 0L500 250L250 500Z" fill="white" fillOpacity="0.9" />
              <path d="M250 450L50 250L250 50L450 250L250 450Z" fill="#003893" />
              <path d="M250 400L100 250L250 100L400 250L250 400Z" fill="#DC143C" />
            </svg>
          </motion.div>

          <motion.h1
            className="text-4xl font-bold mb-4"
            variants={itemVariants}
          >
            {mode === 'login' ? 'Welcome Back!' : 'Join Our Community'}
          </motion.h1>

          <motion.p
            className="text-xl mb-6"
            variants={itemVariants}
          >
            Master Nepali language through interactive lessons, quizzes, and real-time practice.
          </motion.p>

          <motion.div
            className="grid grid-cols-2 gap-4 text-center"
            variants={containerVariants}
          >
            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Interactive Lessons</h3>
              <p className="text-sm mt-2">Learn with engaging, adaptive content</p>
            </motion.div>

            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Speech Recognition</h3>
              <p className="text-sm mt-2">Practice pronunciation with feedback</p>
            </motion.div>

            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Progress Tracking</h3>
              <p className="text-sm mt-2">Monitor your learning journey</p>
            </motion.div>

            <motion.div
              className="p-4 bg-white bg-opacity-10 rounded-lg hover:bg-opacity-20 transition-all"
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
            >
              <h3 className="font-bold text-lg">Community Support</h3>
              <p className="text-sm mt-2">Learn together with peers</p>
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default AuthenticationPage;
