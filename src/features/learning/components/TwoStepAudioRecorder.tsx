import React, { useState } from 'react';
import { useNewAudioSocket } from '@/hooks/useNewAudioSocket';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Mic, Square, Check, X } from 'lucide-react';
import type { SocketConnectionState } from '@/types/socketTypes';

/**
 * Enhanced two-step audio recorder component with animations and theme support
 * Demonstrates the two-step recording process and device selection functionality
 */
const TwoStepAudioRecorder: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [connectionPrepared, setConnectionPrepared] = useState(false);

  // Get the token from localStorage
  const getToken = () => {
    try {
      const userJson = localStorage.getItem("nepali_user");
      if (userJson) {
        const user = JSON.parse(userJson);
        return user.token;
      }
    } catch (error) {
      console.error('Error getting token from localStorage:', error);
    }
    return null;
  };

  // Initialize the new audio Socket.IO hook
  const {
    // Socket.IO state
    connectionState,
    isRecording,
    taskSetId,

    // Recording state
    recording,
    recordingDuration,
    error,

    // Recording methods
    connect,
    startRecording,
    stopRecording,
    cancelRecording
  } = useNewAudioSocket({
    token: getToken(),
    onStateChange: (state: SocketConnectionState) => {
      console.log(`Connection state changed to ${state}`);
    },
    onTaskGenerated: (data) => {
      console.log('Task generated:', data);
    },
    onError: (error) => {
      console.error('Error:', error);
    }
  });

  // Format time in MM:SS format
  const formatTime = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle prepare connection button click
  const handlePrepareConnection = async () => {
    const success = await connect();
    if (success) {
      setConnectionPrepared(true);
    }
  };

  // Handle start recording button click
  const handleStartRecording = async () => {
    // With the new parallel approach, we don't need to prepare the connection first
    // Just start recording and the connection will be established in parallel
    const success = await startRecording();
    if (success) {
      console.log('Recording started successfully');
      // Mark connection as prepared since we're now using the parallel approach
      setConnectionPrepared(true);
    } else {
      console.error('Failed to start recording');
    }
  };

  // Handle stop recording button click
  const handleStopRecording = () => {
    stopRecording();
  };

  // Handle cancel recording button click
  const handleCancelRecording = () => {
    cancelRecording();
    setConnectionPrepared(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn(
        "p-6 rounded-lg shadow-md",
        isDarkMode ? "bg-gray-800 text-gray-100" : "bg-white text-gray-800"
      )}
    >
      <h2 className="text-xl font-bold mb-4">Two-Step Audio Recorder</h2>
      <p className={cn(
        "mb-6",
        isDarkMode ? "text-gray-300" : "text-gray-600"
      )}>
        This component demonstrates the two-step recording process and device selection functionality.
      </p>

      {/* Connection status */}
      <div className={cn(
        "p-4 rounded-lg mb-6 flex items-center",
        connectionState === 'CONNECTED'
          ? (isDarkMode ? "bg-green-900/20 text-green-400" : "bg-green-100 text-green-700")
          : connectionState === 'STARTED' || connectionState === 'ACTIVE'
          ? (isDarkMode ? "bg-blue-900/20 text-blue-400" : "bg-blue-100 text-blue-700")
          : connectionState === 'ERROR'
          ? (isDarkMode ? "bg-red-900/20 text-red-400" : "bg-red-100 text-red-700")
          : (isDarkMode ? "bg-gray-700 text-gray-300" : "bg-gray-100 text-gray-700")
      )}>
        <div className={cn(
          "w-3 h-3 rounded-full mr-2",
          connectionState === 'CONNECTED' ? "bg-green-500" :
          connectionState === 'STARTED' || connectionState === 'ACTIVE' ? "bg-blue-500" :
          connectionState === 'ERROR' ? "bg-red-500" : "bg-gray-400"
        )}></div>
        <span>
          {connectionState === 'CONNECTED' ? 'Connected to server' :
           connectionState === 'STARTED' ? 'Streaming started' :
           connectionState === 'ACTIVE' ? 'Recording active' :
           connectionState === 'ERROR' ? 'Connection error' : 'Disconnected'}
        </span>
      </div>



      {/* Recording controls */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium mb-2">Recording Controls</h3>

        {/* Two-step approach buttons */}
        <div className="flex flex-col gap-4">
          {!connectionPrepared ? (
            <Button
              onClick={handlePrepareConnection}
              className={cn(
                "w-full",
                isDarkMode ? "bg-blue-700 hover:bg-blue-800" : "bg-blue-600 hover:bg-blue-700"
              )}
              disabled={recording}
            >
              <Mic className="mr-2 h-4 w-4" />
              Prepare Connection
            </Button>
          ) : (
            <div className="flex items-center gap-2">
              <Check className="h-5 w-5 text-green-500" />
              <span className={cn(
                isDarkMode ? "text-green-400" : "text-green-600"
              )}>
                Connection prepared
              </span>
            </div>
          )}

          <div className="w-full flex flex-col items-center">
            {!recording ? (
              <Button
                className={cn(
                  "px-6 py-3 w-full justify-center",
                  isDarkMode
                    ? "bg-green-700 hover:bg-green-800 text-white"
                    : "bg-green-500 hover:bg-green-600 text-white"
                )}
                onClick={handleStartRecording}
              >
                <Mic className="mr-2 h-5 w-5" />
                Start Recording
              </Button>
            ) : (
              <div className="flex flex-col items-center w-full">
                <div className={cn(
                  "text-xl font-bold mb-4",
                  isDarkMode ? "text-purple-400" : "text-purple-600"
                )}>Recording...</div>

                {/* Recording time */}
                <div className={cn(
                  "px-3 py-1 rounded-full mb-6",
                  isDarkMode ? "bg-purple-900/30 text-purple-300" : "bg-purple-100 text-purple-800"
                )}>
                  <span className="font-bold">{formatTime(recordingDuration)}</span>
                </div>

                {/* Stop and cancel buttons */}
                <div className="flex gap-4 w-full">
                  <Button
                    className={cn(
                      "flex-1",
                      isDarkMode
                        ? "bg-green-700 hover:bg-green-800"
                        : "bg-green-500 hover:bg-green-600"
                    )}
                    onClick={handleStopRecording}
                  >
                    <Square className="mr-2 h-4 w-4" />
                    Stop Recording
                  </Button>

                  <Button
                    variant="destructive"
                    className="flex-1"
                    onClick={handleCancelRecording}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Processing status */}
      {(connectionState === 'STARTED' || connectionState === 'ACTIVE' || connectionState === 'COMPLETED') && (
        <div className={cn(
          "mt-6 p-4 rounded-lg",
          isDarkMode ? "bg-blue-900/20 text-blue-400" : "bg-blue-100 text-blue-700"
        )}>
          <h3 className="font-medium mb-2">Processing Status</h3>
          <p>
            {connectionState === 'STARTED' && 'Audio streaming started'}
            {connectionState === 'ACTIVE' && 'Recording and processing audio...'}
            {connectionState === 'COMPLETED' && 'Recording completed successfully'}
          </p>
          {taskSetId && (
            <p className="mt-2">
              Task Set ID: <span className="font-mono">{taskSetId}</span>
            </p>
          )}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className={cn(
          "mt-6 p-4 rounded-lg",
          isDarkMode ? "bg-red-900/20 text-red-400" : "bg-red-100 text-red-700"
        )}>
          <h3 className="font-medium mb-2">Error</h3>
          <p>{typeof error === 'string' ? error : error.message || 'An error occurred'}</p>
        </div>
      )}
    </motion.div>
  );
};

export default TwoStepAudioRecorder;
