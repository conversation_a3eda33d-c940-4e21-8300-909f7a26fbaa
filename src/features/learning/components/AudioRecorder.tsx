import React, { useRef, useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNewAudioSocket } from '@/hooks/useNewAudioSocket';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import type { SocketConnectionState, TaskGeneratedEventPayload, TaskGenerationFailedEventPayload } from '@/types/socketTypes';

// Define animation keyframes for audio visualization
const animationStyles = `
  @keyframes pulse-slow {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(1.5); }
  }
  @keyframes pulse-medium {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(1.8); }
  }
  @keyframes pulse-fast {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(2); }
  }
  .animate-pulse-slow { animation: pulse-slow 2.5s infinite; }
  .animate-pulse-medium { animation: pulse-medium 1.8s infinite; }
  .animate-pulse-fast { animation: pulse-fast 1.2s infinite; }
`;

interface AudioRecorderProps {
  onRecordingComplete?: (taskSetId?: string) => void;
  chunkDurationMs?: number; // Duration of each audio chunk in milliseconds
}

/**
 * Enhanced audio recorder component with animations and theme support
 */
const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onRecordingComplete,
  chunkDurationMs = 5000 // Default to 5 seconds per chunk
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Refs
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Handle task generation
  const handleTaskGenerated = useCallback((data: TaskGeneratedEventPayload) => {
    console.log('Task generated:', data);

    if (onRecordingComplete) {
      onRecordingComplete(data.task_set_id);
    }

    // Navigate to task view
    setTimeout(() => {
      navigate(`/tasks/${data.task_set_id}`, { state: { from: 'learning' } });
    }, 1500);
  }, [onRecordingComplete, navigate]);

  // Handle task generation failure
  const handleTaskGenerationFailed = useCallback((data: TaskGenerationFailedEventPayload) => {
    console.error('Task generation failed:', data);

    // Show error message to user
    // The error will be displayed in the error section of the component

    // Optionally call onRecordingComplete with no task set ID to indicate failure
    if (onRecordingComplete) {
      onRecordingComplete(undefined);
    }
  }, [onRecordingComplete]);

  // Track progress
  const [progressPercentage, setProgressPercentage] = useState<number>(0);

  // Handle chunk acknowledgment
  const handleChunkAcknowledged = useCallback((data: { chunk_number: number, bytes_received: number, total_bytes_received: number, timestamp: string }) => {
    console.log(`Chunk #${data.chunk_number} acknowledged, total bytes: ${data.total_bytes_received}`);
  }, []);

  // Handle connection state changes
  const handleStateChange = useCallback((state: SocketConnectionState) => {
    console.log('Connection state changed:', state);
  }, []);

  // Initialize New Audio Socket.IO hook
  const {
    connectionState,
    isRecording,
    recording,
    audioUrl,
    error,
    chunksAcknowledged,
    totalBytesAcknowledged,
    taskSetId,
    startRecording,
    stopRecording,
    cancelRecording,
    disconnect: closeConnection,
    recordingDuration,
    availableDevices,
    selectedDeviceId,
    setAudioDevice,
    getAudioDevices,
    permissionGranted
  } = useNewAudioSocket({
    token: (user as any)?.token || null,
    onStateChange: handleStateChange,
    onChunkReceived: handleChunkAcknowledged,
    onTaskGenerated: handleTaskGenerated,
    onTaskGenerationFailed: handleTaskGenerationFailed,
    onError: (error) => {
      console.error('Socket error:', error);
    }
  });

  // Clean up on unmount
  useEffect(() => {
    return () => {
      closeConnection();
    };
  }, [closeConnection]);



  // Format time in MM:SS format
  const formatTime = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn(
        "flex flex-col items-center w-full max-w-2xl mx-auto p-6 rounded-lg shadow",
        isDarkMode ? "bg-gray-800" : "bg-white"
      )}
    >
      {/* Animation styles */}
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />

      {/* Header */}
      <div className="flex items-center w-full mb-6">
        <div className={cn(
          "w-12 h-12 rounded-full flex items-center justify-center",
          isDarkMode ? "bg-purple-900/30" : "bg-purple-100"
        )}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className={cn(
            "w-6 h-6 fill-current",
            isDarkMode ? "text-purple-400" : "text-purple-500"
          )}>
            <path d="M12 16a4 4 0 0 0 4-4V6a4 4 0 1 0-8 0v6a4 4 0 0 0 4 4zm0-12a2 2 0 0 1 2 2v6a2 2 0 1 1-4 0V6a2 2 0 0 1 2-2z" />
            <path d="M19 12a1 1 0 0 0-2 0 5 5 0 0 1-10 0 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H8a1 1 0 0 0 0 2h8a1 1 0 0 0 0-2h-3v-1.08A7 7 0 0 0 19 12z" />
          </svg>
        </div>
        <div className="ml-4">
          <h1 className={cn(
            "text-2xl font-bold",
            isDarkMode ? "text-purple-400" : "text-purple-500"
          )}>Audio Recorder</h1>
          <p className={cn(
            isDarkMode ? "text-gray-300" : "text-gray-600"
          )}>Record and play audio</p>
        </div>

        {/* Socket.IO status indicator */}
        <div className="ml-auto flex items-center">
          <div className={`w-3 h-3 rounded-full mr-2 ${
            connectionState === 'CONNECTED' ? 'bg-green-500' :
            connectionState === 'STARTED' || connectionState === 'ACTIVE' ? 'bg-blue-500' :
            connectionState === 'COMPLETED' ? 'bg-purple-500' :
            connectionState === 'ERROR' ? 'bg-red-500' : 'bg-gray-400'
          }`}></div>
          <span className={cn(
            "text-xs",
            isDarkMode ? "text-gray-300" : "text-gray-500"
          )}>
            {connectionState === 'CONNECTED' ? 'Connected' :
             connectionState === 'STARTED' ? 'Streaming Started' :
             connectionState === 'ACTIVE' ? 'Recording Active' :
             connectionState === 'COMPLETED' ? 'Completed' :
             connectionState === 'ERROR' ? 'Connection Error' : 'Disconnected'}
          </span>
        </div>
      </div>



      {/* Processing status banner */}
      {(connectionState === 'STARTED' || connectionState === 'ACTIVE' || connectionState === 'COMPLETED') && (
        <div className={cn(
          "w-full p-3 rounded-lg mb-4",
          isDarkMode ? "bg-blue-900/20 text-blue-400" : "bg-blue-100 text-blue-700"
        )}>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-blue-500 mr-2 animate-pulse"></div>
            <span>
              {connectionState === 'STARTED' && 'Audio streaming started'}
              {connectionState === 'ACTIVE' && 'Recording and processing audio...'}
              {connectionState === 'COMPLETED' && 'Recording completed successfully'}
            </span>
          </div>

          {/* Show progress bar for active recording */}
          {connectionState === 'ACTIVE' && (
            <div className="mt-2">
              <div className="w-full bg-blue-200 rounded-full h-2.5 dark:bg-blue-700">
                <div
                  className="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500"
                  style={{ width: `${progressPercentage || 0}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs mt-1">
                <span>{progressPercentage || 0}% complete</span>
                {chunksAcknowledged > 0 && (
                  <span>{chunksAcknowledged} chunks ({(totalBytesAcknowledged / 1024).toFixed(1)} KB)</span>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Main content area */}
      <div className={cn(
        "w-full border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center",
        isDarkMode ? "border-purple-700" : "border-purple-300"
      )}>
        {audioUrl ? (
          <div className="w-full">
            {/* Audio player */}
            <div className="flex items-center justify-center mb-4">
              <audio ref={audioRef} src={audioUrl} controls className="w-full" />
            </div>

            {/* Record new button */}
            <div className="flex justify-center mt-4">
              <button
                onClick={startRecording}
                className={cn(
                  "text-white px-4 py-2 rounded-lg transition-colors flex items-center",
                  isDarkMode ? "bg-purple-700 hover:bg-purple-800" : "bg-purple-500 hover:bg-purple-600"
                )}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                Record New
              </button>
            </div>
          </div>
        ) : (
          <>
            {/* Recording UI */}
            {recording ? (
              <div className="flex flex-col items-center">
                <div className={cn(
                  "text-xl font-bold mb-4",
                  isDarkMode ? "text-purple-400" : "text-purple-600"
                )}>Recording...</div>

                {/* Recording animation */}
                <div className="flex space-x-1 mb-4">
                  <div className={cn(
                    "w-1 h-8 rounded-full animate-pulse-slow",
                    isDarkMode ? "bg-purple-700" : "bg-purple-300"
                  )}></div>
                  <div className={cn(
                    "w-1 h-16 rounded-full animate-pulse-medium",
                    isDarkMode ? "bg-purple-600" : "bg-purple-400"
                  )}></div>
                  <div className={cn(
                    "w-1 h-12 rounded-full animate-pulse-fast",
                    isDarkMode ? "bg-purple-500" : "bg-purple-500"
                  )}></div>
                  <div className={cn(
                    "w-1 h-20 rounded-full animate-pulse-medium",
                    isDarkMode ? "bg-purple-400" : "bg-purple-600"
                  )}></div>
                  <div className={cn(
                    "w-1 h-10 rounded-full animate-pulse-slow",
                    isDarkMode ? "bg-purple-500" : "bg-purple-500"
                  )}></div>
                  <div className={cn(
                    "w-1 h-14 rounded-full animate-pulse-fast",
                    isDarkMode ? "bg-purple-600" : "bg-purple-400"
                  )}></div>
                  <div className={cn(
                    "w-1 h-6 rounded-full animate-pulse-medium",
                    isDarkMode ? "bg-purple-700" : "bg-purple-300"
                  )}></div>
                </div>

                {/* Recording time and chunks info */}
                <div className="flex flex-col items-center mb-6 space-y-2">
                  <div className={cn(
                    "px-3 py-1 rounded-full",
                    isDarkMode ? "bg-purple-900/30 text-purple-300" : "bg-purple-100 text-purple-800"
                  )}>
                    <span className="font-bold">{formatTime(recordingDuration)}</span>
                  </div>

                  {/* Chunks info */}
                  {chunksAcknowledged > 0 && (
                    <div className={cn(
                      "text-xs px-3 py-1 rounded-full",
                      isDarkMode ? "bg-blue-900/30 text-blue-300" : "bg-blue-100 text-blue-800"
                    )}>
                      <span>
                        {chunksAcknowledged} chunks sent ({(totalBytesAcknowledged / 1024).toFixed(1)} KB)
                      </span>
                    </div>
                  )}
                </div>

                {/* Stop and cancel buttons */}
                <div className="flex space-x-4">
                  <button
                    onClick={stopRecording}
                    className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Stop & Save
                  </button>

                  <button
                    onClick={cancelRecording}
                    className="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors flex items-center"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              /* Start recording button */
              <div className="flex flex-col items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className={cn(
                  "h-16 w-16 mb-4",
                  isDarkMode ? "text-purple-700" : "text-purple-300"
                )} viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
                <h3 className={cn(
                  "text-lg font-medium mb-4",
                  isDarkMode ? "text-gray-200" : "text-gray-700"
                )}>Ready to Record</h3>
                <button
                  onClick={startRecording}
                  className={cn(
                    "text-white px-6 py-3 rounded-lg transition-colors flex items-center text-lg",
                    isDarkMode ? "bg-purple-700 hover:bg-purple-800" : "bg-purple-500 hover:bg-purple-600"
                  )}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                  </svg>
                  Start Recording
                </button>
              </div>
            )}
          </>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className={cn(
          "mt-4 p-3 rounded-lg w-full",
          isDarkMode ? "bg-red-900/20 text-red-400" : "bg-red-100 text-red-700"
        )}>
          {typeof error === 'string' ? error : error.message || 'An error occurred'}
        </div>
      )}
    </motion.div>
  );
};

export default AudioRecorder;
