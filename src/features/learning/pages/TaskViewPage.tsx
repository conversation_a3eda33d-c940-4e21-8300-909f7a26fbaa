import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { fetchTaskSet, fetchTask, fetchTaskSetScore, TaskSet, Task, ScoreResponse } from '@/api/taskService';
import { getFileUrl } from '@/api/fileService';
import TaskItem from '@/features/tasks/components/TaskItem';
import MainLayout from '@/components/layout/MainLayout';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';

/**
 * Enhanced task view page with animations and theme support
 */
const TaskViewPage: React.FC = () => {
  const { taskSetId } = useParams<{ taskSetId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAuthenticated } = useAuth();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [taskSet, setTaskSet] = useState<TaskSet | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [currentTaskIndex, setCurrentTaskIndex] = useState<number>(0);
  const [audioPreviewUrl, setAudioPreviewUrl] = useState<string | null>(null);
  const [loadingTasks, setLoadingTasks] = useState<boolean>(false);

  // Score related states
  const [scores, setScores] = useState<any>(null);
  const [totalScore, setTotalScore] = useState<number>(0);
  const [maxScore, setMaxScore] = useState<number>(0);
  const [loadingScores, setLoadingScores] = useState<boolean>(false);

  // Fetch task set data when component mounts
  useEffect(() => {
    if (!isAuthenticated || !user) {
      navigate('/');
      return;
    }

    if (!taskSetId) {
      setError('No task set ID provided');
      setLoading(false);
      return;
    }

    // Check if we came from a direct URL (no state passed)
    const isDirectNavigation = !location.state;

    const loadTaskSet = async () => {
      try {
        setLoading(true);
        console.log('Starting to fetch task set with ID:', taskSetId);
        console.log('Direct navigation (from URL):', isDirectNavigation);

        // Fetch the task set without including full tasks (for faster initial load)
        const taskSetData = await fetchTaskSet(taskSetId, user, { include_tasks: false });
        console.log('Task set data received:', taskSetData);
        setTaskSet(taskSetData);

        // Process input metadata for audio preview in parallel with task loading
        let audioPreviewPromise = null;
        if (taskSetData.input_metadata) {
          console.log('Input metadata found, processing in parallel:', taskSetData.input_metadata);
          const { object_name, folder } = taskSetData.input_metadata;

          if (object_name && folder) {
            // Start the audio URL fetch but don't await it yet - we'll process it in parallel
            console.log(`Fetching audio URL for object: ${object_name}, folder: ${folder}`);
            audioPreviewPromise = getFileUrl(object_name, folder, user);
          }
        }

        // Check for tasks in the response
        if (taskSetData.tasks && taskSetData.tasks.length > 0) {
          console.log('Tasks found in response:', taskSetData.tasks);

          // If tasks are already included in the response, use them directly
          // Convert task IDs to actual task objects if they're not already
          if (typeof taskSetData.tasks[0] === 'string') {
            // We have task IDs, need to fetch the first task
            console.log('Tasks are IDs, fetching first task');
            setLoadingTasks(true);
            try {
              const firstTask = await fetchTask(taskSetData.tasks[0], user);
              console.log('First task loaded:', firstTask);

              // Set the first task only
              setTasks([firstTask]);
              setLoadingTasks(false);
            } catch (firstTaskErr) {
              console.error('Error loading first task:', firstTaskErr);
              setError('Failed to load task. Please try again.');
              setLoadingTasks(false);
            }
          } else {
            // Tasks are already full objects, use them directly
            console.log('Tasks are full objects, using directly');
            setTasks([taskSetData.tasks[0]]);
          }
        } else {
          console.error('No tasks found in task set');
          setError('No tasks found in this task set');
        }

        // Now resolve the audio preview promise if it exists
        if (audioPreviewPromise) {
          try {
            console.log('Resolving audio preview promise');
            const audioUrl = await audioPreviewPromise;
            console.log('Audio URL received:', audioUrl);
            setAudioPreviewUrl(audioUrl);
          } catch (audioErr) {
            console.error('Error fetching audio preview:', audioErr);
            // Don't set an error state, just log it - audio preview is optional
          }
        } else {
          console.log('No audio preview to load');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading task set:', err);
        setError('Failed to load task set. Please try again.');
        setLoading(false);
      }
    };

    loadTaskSet();
  }, [taskSetId, user, isAuthenticated, navigate, location.state]);

  // Navigate to next task
  const goToNextTask = async () => {
    if (!taskSet?.tasks) return;

    const nextIndex = currentTaskIndex + 1;
    if (nextIndex < taskSet.tasks.length) {
      // Check if we already have the next task loaded
      if (nextIndex < tasks.length) {
        // We already have this task loaded, just navigate to it
        console.log(`Navigating to already loaded task at index ${nextIndex}`);
        setCurrentTaskIndex(nextIndex);
      } else {
        // We need to fetch the next task
        setLoadingTasks(true);
        try {
          // Check if the task is a string (ID) or an object
          const nextTask = typeof taskSet.tasks[nextIndex] === 'string'
            ? await fetchTask(taskSet.tasks[nextIndex], user)
            : taskSet.tasks[nextIndex];

          console.log('Next task loaded:', nextTask);

          // Add the new task to our tasks array
          setTasks(prevTasks => [...prevTasks, nextTask]);

          // Navigate to the new task
          setCurrentTaskIndex(nextIndex);
        } catch (error) {
          console.error('Error fetching next task:', error);
          setError('Failed to load next task. Please try again.');
          // Don't navigate if we couldn't fetch the task
        } finally {
          setLoadingTasks(false);
        }
      }
    }
  };

  // Navigate to previous task
  const goToPreviousTask = () => {
    if (currentTaskIndex > 0) {
      console.log(`Navigating to previous task at index ${currentTaskIndex - 1}`);
      setCurrentTaskIndex(currentTaskIndex - 1);
    }
  };

  // Fetch test scores using the new API
  const fetchScores = async () => {
    if (!taskSetId || !user) return;

    try {
      setLoadingScores(true);

      // Use the new fetchTaskSetScore method that returns {score, total_score, percentage, total_tasks, attempted_tasks, status}
      const scoreData: ScoreResponse = await fetchTaskSetScore(taskSetId, user);
      console.log('Task set score data:', scoreData);
      console.log('Raw score values - score:', scoreData.score, 'total_score:', scoreData.total_score);

      // Create a scores object for compatibility with existing UI components
      const finalScore = scoreData.score ?? 0;
      const finalMaxScore = scoreData.total_score ?? scoreData.max_score ?? 0; // Handle both task set and task item formats
      setScores({
        task_set_id: taskSetId,
        total_score: finalMaxScore,
        scored: finalScore,
        percentage: scoreData.percentage ?? (finalMaxScore > 0 ? Math.round((finalScore / finalMaxScore) * 100) : 0),
        status: scoreData.status ?? 'in_progress'
      });

      // Set the total score and max score from the new API response format
      console.log(`Setting totalScore=${finalScore}, maxScore=${finalMaxScore}`);
      console.log('Before setting state - totalScore:', totalScore, 'maxScore:', maxScore);
      setTotalScore(finalScore);
      setMaxScore(finalMaxScore);
      console.log('After setting state - should be totalScore:', finalScore, 'maxScore:', finalMaxScore);

    } catch (error) {
      console.error('Error fetching scores:', error);
    } finally {
      setLoadingScores(false);
    }
  };

  // Fetch scores when component mounts
  useEffect(() => {
    if (taskSetId && user && isAuthenticated) {
      fetchScores();
    }
  }, [taskSetId, user, isAuthenticated]);

  // Handle answer submission
  const handleAnswerSubmitted = (taskId: string, isCorrect: boolean) => {
    console.log(`Answer submitted for task ${taskId}. Correct: ${isCorrect}`);
    // Fetch updated scores after answer submission
    fetchScores();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.5 }
    },
    exit: {
      opacity: 0,
      transition: { duration: 0.3 }
    }
  };

  // Render current task based on its type
  const renderCurrentTask = () => {
    if (tasks.length === 0) {
      if (loadingTasks) {
        return (
          <div className={cn(
            "p-6 rounded-lg shadow-md flex flex-col items-center justify-center",
            isDarkMode ? "bg-gray-800" : "bg-white"
          )}>
            <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
            <p className={cn(isDarkMode ? "text-gray-300" : "text-gray-600")}>Loading task...</p>
          </div>
        );
      }
      return null;
    }

    // Check if the current task index is valid
    if (currentTaskIndex >= tasks.length) {
      if (loadingTasks) {
        return (
          <div className={cn(
            "p-6 rounded-lg shadow-md flex flex-col items-center justify-center",
            isDarkMode ? "bg-gray-800" : "bg-white"
          )}>
            <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
            <p className={cn(isDarkMode ? "text-gray-300" : "text-gray-600")}>Loading next task...</p>
          </div>
        );
      }

      // If we have tasks but the current task isn't loaded yet, try to load it
      if (taskSet?.tasks && currentTaskIndex < taskSet.tasks.length) {
        // Trigger loading of the current task
        const loadCurrentTask = async () => {
          setLoadingTasks(true);
          try {
            // Check if the task is a string (ID) or an object
            const currentTaskData = taskSet.tasks[currentTaskIndex];
            const task = typeof currentTaskData === 'string'
              ? await fetchTask(currentTaskData, user)
              : currentTaskData;

            console.log(`Task at index ${currentTaskIndex} loaded:`, task);
            setTasks(prevTasks => [...prevTasks, task]);
          } catch (error) {
            console.error('Error fetching task:', error);
            setError('Failed to load task. Please try refreshing the page.');
          } finally {
            setLoadingTasks(false);
          }
        };

        // Call the function to load the task
        loadCurrentTask();

        // Show loading indicator while fetching
        return (
          <div className={cn(
            "p-6 rounded-lg shadow-md flex flex-col items-center justify-center",
            isDarkMode ? "bg-gray-800" : "bg-white"
          )}>
            <Loader2 className="h-8 w-8 animate-spin mb-4 text-primary" />
            <p className={cn(isDarkMode ? "text-gray-300" : "text-gray-600")}>Loading task...</p>
          </div>
        );
      }

      return (
        <div className={cn(
          "p-4 rounded-lg",
          isDarkMode ? "bg-yellow-900/20 text-yellow-200" : "bg-yellow-100 text-yellow-800"
        )}>
          <p>Task not found. Please try refreshing the page.</p>
        </div>
      );
    }

    const currentTask = tasks[currentTaskIndex];

    // Since the new API doesn't provide individual task scores,
    // we'll calculate an average score per task
    const averageScorePerTask = scores && scores.total_score > 0 && tasks.length > 0
      ? Math.round(scores.scored / tasks.length)
      : 0;

    // Use the TaskItem component to render the current task
    return (
      <TaskItem
        task={currentTask}
        onAnswerSubmitted={handleAnswerSubmitted}
        onNext={goToNextTask}
        onPrevious={goToPreviousTask}
        score={averageScorePerTask}
        maxScore={scores?.total_score ? Math.round(scores.total_score / tasks.length) : 10} // Average max score per task
        showPrevious={true}
        showNext={true}
        isFirstTask={currentTaskIndex === 0}
        isLastTask={currentTaskIndex >= (taskSet?.tasks?.length || 0) - 1}
        isLoadingTasks={loadingTasks}
      />
    );
  };

  return (
    <MainLayout
      title="Task Practice"
      description="Complete your learning tasks"
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={loading ? 'loading' : error ? 'error' : 'content'}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className={cn(
              "container mx-auto p-6",
              isDarkMode ? "bg-red-900/20 border border-red-800 text-red-200" : "bg-red-100 border border-red-400 text-red-700"
            )}>
              <p>{error}</p>
              <Button
                className={cn(
                  "mt-4",
                  isDarkMode ? "bg-red-800 hover:bg-red-700 text-white" : "bg-red-500 hover:bg-red-600 text-white"
                )}
                onClick={() => navigate('/dashboard')}
              >
                Return to Dashboard
              </Button>
            </div>
          ) : (
            <div className="container mx-auto p-3">
              <div className="mb-3">
                <div className="flex justify-between items-center">
                  {/* Score display */}
                  <div className="flex items-center ml-auto">
                    {loadingScores ? (
                      <div className="flex items-center text-gray-500 text-base">
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <div className={cn(
                          "text-lg font-semibold",
                          isDarkMode ? "text-purple-300" : "text-purple-700"
                        )}>
                          Score: {totalScore ?? 0}/{maxScore ?? 0}
                        </div>
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 ml-3">
                          <div
                            className="bg-purple-600 dark:bg-purple-500 h-2.5 rounded-full"
                            style={{ width: `${maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0}%` }}
                          ></div>
                        </div>
                        {maxScore > 0 && (
                          <div className={cn(
                            "text-sm font-medium ml-2",
                            isDarkMode ? "text-gray-300" : "text-gray-600"
                          )}>
                            {Math.round((totalScore / maxScore) * 100)}%
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Audio preview section */}
                {audioPreviewUrl ? (
                  <div className={cn(
                    "mt-2 mb-3 p-2 rounded shadow-sm",
                    isDarkMode ? "bg-gray-800" : "bg-gray-50"
                  )}>
                    <h3 className={cn(
                      "text-sm font-medium mb-1",
                      isDarkMode ? "text-gray-200" : "text-gray-800"
                    )}>
                      Your Recording:
                    </h3>
                    <audio controls className="w-full h-8">
                      <source src={audioPreviewUrl} type="audio/mpeg" />
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                ) : taskSet?.input_metadata?.object_name ? (
                  <div className={cn(
                    "mt-2 mb-3 p-2 rounded border",
                    isDarkMode ? "bg-gray-800/50 border-gray-700" : "bg-gray-100 border-gray-200"
                  )}>
                    <h3 className={cn(
                      "text-sm font-medium mb-1",
                      isDarkMode ? "text-gray-200" : "text-gray-800"
                    )}>
                      Audio Preview:
                    </h3>
                    <p className={cn(
                      "text-xs",
                      isDarkMode ? "text-gray-400" : "text-gray-600"
                    )}>
                      Loading audio preview...
                    </p>
                    <div className="animate-pulse rounded-full h-3 w-3 border-t-2 border-b-2 border-purple-500 mx-auto mt-1"></div>
                  </div>
                ) : null}

                <div className="flex items-center mt-2 mb-3">
                  <div className="flex space-x-2">
                    {taskSet?.tasks?.map((_, index) => (
                      <div
                        key={index}
                        className={`w-3 h-3 rounded-full ${
                          index === currentTaskIndex
                            ? 'bg-purple-600 dark:bg-purple-500'
                            : index < currentTaskIndex
                              ? 'bg-purple-400 dark:bg-purple-400'
                              : index < tasks.length
                                ? isDarkMode ? 'bg-gray-600' : 'bg-gray-300'
                                : isDarkMode ? 'bg-gray-700' : 'bg-gray-400'
                        }`}
                      ></div>
                    ))}
                    {loadingTasks && (
                      <div className="w-3 h-3 rounded-full bg-gray-400 dark:bg-gray-500 animate-pulse ml-2"></div>
                    )}
                  </div>
                  <div className={cn(
                    "ml-auto text-sm flex items-center font-medium",
                    isDarkMode ? "text-gray-300" : "text-gray-600"
                  )}>
                    {loadingTasks && (
                      <Loader2 className="mr-2 w-4 h-4 animate-spin" />
                    )}
                    Task {currentTaskIndex + 1} of {taskSet?.tasks?.length || 0}
                  </div>
                </div>
              </div>

              {renderCurrentTask()}
            </div>
          )}
        </motion.div>
      </AnimatePresence>
    </MainLayout>
  );
};

export default TaskViewPage;
