import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import AnimatedPage from '@/components/common/AnimatedPage';
import AudioRecorder from '@/features/learning/components/AudioRecorder';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';

/**
 * Enhanced begin learning page with animations and theme support
 */
const BeginLearningPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [taskSetId, setTaskSetId] = useState<string | undefined>(undefined);
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Handle recording completion
  const handleRecordingComplete = (newTaskSetId?: string) => {
    if (newTaskSetId) {
      setTaskSetId(newTaskSetId);
    }
  };

  // Animation variants
  const successVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 25
      }
    },
    exit: { 
      opacity: 0, 
      y: -20,
      transition: { duration: 0.3 }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <MainLayout
      title="Begin Learning"
      description="Start your Nepali learning journey with audio recording"
    >
      <AnimatedPage>
        <div className="max-w-3xl mx-auto">
          <AnimatePresence>
            {taskSetId && (
              <motion.div
                key="success-message"
                variants={successVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className={cn(
                  "mb-6 p-4 rounded-lg text-center",
                  isDarkMode
                    ? "bg-green-900/20 text-green-400 border border-green-800"
                    : "bg-green-100 text-green-700 border border-green-200"
                )}
              >
                <div className="flex items-center justify-center gap-2 font-medium">
                  <svg
                    className="h-5 w-5"
                    fill="none"
                    height="24"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                    width="24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                    <polyline points="22 4 12 14.01 9 11.01" />
                  </svg>
                  Recording processed successfully!
                </div>
                <div className="mt-1 text-sm">
                  Task Set ID: <span className="font-mono">{taskSetId}</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {user?.token ? (
            <motion.div 
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className={cn(
                "p-6 rounded-lg shadow-md",
                isDarkMode
                  ? "bg-gray-800 border border-gray-700"
                  : "bg-white border border-gray-200"
              )}
            >
              <motion.h2 
                variants={itemVariants}
                className={cn(
                  "text-xl font-bold mb-4",
                  isDarkMode ? "text-gray-100" : "text-gray-800"
                )}
              >
                Record Your Voice
              </motion.h2>
              
              <motion.p 
                variants={itemVariants}
                className={cn(
                  "mb-6",
                  isDarkMode ? "text-gray-300" : "text-gray-600"
                )}
              >
                Speak clearly into your microphone. The system will analyze your pronunciation and provide feedback.
              </motion.p>

              <motion.div variants={itemVariants}>
                <AudioRecorder onRecordingComplete={handleRecordingComplete} />
              </motion.div>

              <motion.div 
                variants={itemVariants}
                className={cn(
                  "mt-6 p-4 rounded-lg text-sm",
                  isDarkMode
                    ? "bg-blue-900/20 text-blue-300 border border-blue-800"
                    : "bg-blue-50 text-blue-700 border border-blue-100"
                )}
              >
                <h3 className="font-medium mb-2">Tips for better results:</h3>
                <ul className="list-disc list-inside space-y-1">
                  <li>Use a good quality microphone</li>
                  <li>Record in a quiet environment</li>
                  <li>Speak clearly and at a normal pace</li>
                  <li>Position yourself 6-12 inches from the microphone</li>
                </ul>
              </motion.div>
            </motion.div>
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Authentication Error</AlertTitle>
              <AlertDescription>
                Please try logging out and logging back in.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </AnimatedPage>
    </MainLayout>
  );
};

export default BeginLearningPage;
