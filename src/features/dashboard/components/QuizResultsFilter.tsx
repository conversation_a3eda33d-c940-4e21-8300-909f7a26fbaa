import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Search, CheckCircle, AlertCircle, ListFilter } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTheme } from '@/core/hooks/useTheme';
import { motion } from 'framer-motion';

interface QuizResultsFilterProps {
  filter: string;
  searchQuery: string;
  onFilterChange: (value: string) => void;
  onSearchChange: (value: string) => void;
}

/**
 * Filter component for quiz results
 */
const QuizResultsFilter: React.FC<QuizResultsFilterProps> = ({
  filter,
  searchQuery,
  onFilterChange,
  onSearchChange,
}) => {
  const { isDarkMode } = useTheme();

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="mb-6"
    >
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <Tabs 
          value={filter} 
          onValueChange={onFilterChange}
          className={cn(
            "w-full sm:w-auto",
            isDarkMode ? "text-gray-200" : "text-gray-800"
          )}
        >
          <TabsList className="grid grid-cols-3 w-full sm:w-auto">
            <TabsTrigger 
              value="all" 
              className="flex items-center gap-1.5"
            >
              <ListFilter className="h-4 w-4" />
              <span>All</span>
            </TabsTrigger>
            <TabsTrigger 
              value="completed" 
              className="flex items-center gap-1.5"
            >
              <CheckCircle className="h-4 w-4" />
              <span>Completed</span>
            </TabsTrigger>
            <TabsTrigger 
              value="pending" 
              className="flex items-center gap-1.5"
            >
              <AlertCircle className="h-4 w-4" />
              <span>Pending</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="relative w-full sm:w-64">
          <Input
            placeholder="Search quizzes..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className={cn(
              "pl-9 h-9",
              isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
            )}
          />
          <Search className={cn(
            "absolute left-3 top-2.5 h-4 w-4",
            isDarkMode ? "text-gray-400" : "text-gray-500"
          )} />
        </div>
      </div>
    </motion.div>
  );
};

export default QuizResultsFilter;
