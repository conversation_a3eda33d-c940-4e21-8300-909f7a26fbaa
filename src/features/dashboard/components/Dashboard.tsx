import React, { useEffect } from "react";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger, SidebarInset } from '@/components/ui/sidebar';
import { motion } from "framer-motion";
import SideNavigation from '@/features/navigation/components/SideNavigation';

/**
 * Dashboard component with enhanced UI features
 * Displays user information and navigation cards
 */
const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Animation variants for staggered card animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  // Page transition animation
  const pageVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.5 } },
    exit: { opacity: 0, transition: { duration: 0.3 } }
  };

  return (
    <SidebarProvider>
      <motion.div 
        className="flex min-h-screen w-full bg-gray-50 dark:bg-gray-900"
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageVariants}
      >
        <SideNavigation />
        <SidebarInset>
          <div className="flex flex-col h-screen">
            {/* Page header with animation */}
            <motion.div 
              className="bg-white dark:bg-gray-800 shadow-sm p-4"
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-2xl font-bold text-nepali-maroon dark:text-nepali-red">Dashboard</h1>
                  <p className="text-gray-500 dark:text-gray-400">Welcome back, {user?.username}!</p>
                </div>
                <SidebarTrigger />
              </div>
            </motion.div>

            {/* Page content with card animations */}
            <main className="flex-1 p-6 overflow-auto">
              <motion.div 
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <motion.div variants={cardVariants}>
                  <Card className="shadow-md hover:shadow-lg transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
                    <CardHeader className="bg-nepali-red bg-opacity-10 dark:bg-opacity-20 pb-2">
                      <CardTitle className="text-xl text-nepali-maroon dark:text-nepali-red">Begin Learning</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <p className="text-gray-600 dark:text-gray-300">
                        Start your Nepali learning journey today. Practice speaking with our real-time audio recording.
                      </p>
                      <div className="mt-4">
                        <Button 
                          className="nepali-gradient text-white hover:shadow-md transition-all"
                          onClick={() => navigate("/begin-learning")}
                        >
                          Start Now
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div variants={cardVariants}>
                  <Card className="shadow-md hover:shadow-lg transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
                    <CardHeader className="bg-nepali-blue bg-opacity-10 dark:bg-opacity-20 pb-2">
                      <CardTitle className="text-xl text-nepali-blue">My Progress</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <p className="text-gray-600 dark:text-gray-300">
                        View your learning statistics and track your improvement over time.
                      </p>
                      <div className="mt-4">
                        <Button
                          variant="outline"
                          className="border-nepali-blue text-nepali-blue hover:bg-nepali-blue hover:text-white dark:border-nepali-blue/70 dark:text-nepali-blue/90 dark:hover:bg-nepali-blue/20"
                          onClick={() => navigate("/progress")}
                        >
                          View Stats
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div variants={cardVariants}>
                  <Card className="shadow-md hover:shadow-lg transition-all duration-300 dark:bg-gray-800 dark:border-gray-700">
                    <CardHeader className="bg-purple-500 bg-opacity-10 dark:bg-opacity-20 pb-2">
                      <CardTitle className="text-xl text-purple-700 dark:text-purple-400">Task Sets</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-4">
                      <p className="text-gray-600 dark:text-gray-300">
                        View and manage your task sets. Track your progress on various learning tasks.
                      </p>
                      <div className="mt-4">
                        <Button
                          variant="outline"
                          className="border-purple-500 text-purple-700 hover:bg-purple-500 hover:text-white dark:border-purple-400 dark:text-purple-400 dark:hover:bg-purple-500/20"
                          onClick={() => navigate("/tasks")}
                        >
                          View Tasks
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>
            </main>
          </div>
        </SidebarInset>
      </motion.div>
    </SidebarProvider>
  );
};

export default Dashboard;
