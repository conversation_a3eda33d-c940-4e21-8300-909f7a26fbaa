import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { useTheme } from '@/core/hooks/useTheme';
import { useNavigate } from 'react-router-dom';
import { Progress } from '@/components/ui/progress';

interface QuizResultsCardProps {
  quiz: {
    id?: string;
    _id?: string;
    status: string;
    scored?: number;
    max_score?: number;
    created_at: string;
    tasks: string[];
    input_type?: string;
    input_content?: string;
  };
  index: number;
}

/**
 * Card component to display individual quiz results
 */
const QuizResultsCard: React.FC<QuizResultsCardProps> = ({ quiz, index }) => {
  const { isDarkMode } = useTheme();
  const navigate = useNavigate();

  // Debug the quiz data
  console.log(`Quiz ${index} data:`, quiz);
  console.log(`Quiz ${index} tasks:`, quiz.tasks);

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy • h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  // Determine if quiz is completed
  const isCompleted = quiz.status === 'completed';

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        delay: index * 0.05 // Stagger effect
      }
    }
  };

  // Handle card click to navigate to quiz details
  const handleCardClick = () => {
    navigate(`/task-sets/${quiz._id || quiz.id}`);
  };

  // Calculate progress percentage based on score and tasks
  const calculateProgress = () => {
    // Check if tasks exists and has length
    if (!quiz.tasks) {
      console.error('Quiz tasks is undefined or null:', quiz);
      return isCompleted ? 100 : 0;
    }

    // If we have a score and tasks, use that for progress
    if (quiz.scored !== undefined && quiz.tasks.length) {
      return Math.round((quiz.scored / quiz.tasks.length) * 100);
    }

    // If no score but we have tasks and it's completed, show 100%
    if (isCompleted && quiz.tasks.length > 0) {
      return 100;
    }

    // Default progress based on status
    return isCompleted ? 100 : 0;
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <Card
        className={cn(
          "overflow-hidden cursor-pointer hover:shadow-md transition-all",
          isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
        )}
        onClick={handleCardClick}
      >
        <CardHeader className={cn(
          "pb-2 flex flex-row items-center justify-between",
          isCompleted
            ? isDarkMode ? "bg-green-900/20" : "bg-green-50"
            : isDarkMode ? "bg-amber-900/20" : "bg-amber-50"
        )}>
          <CardTitle className="text-lg">Quiz #{(quiz._id || quiz.id || '').slice(-6)}</CardTitle>
          <Badge
            variant={isCompleted ? "success" : "warning"}
            className="ml-2"
          >
            <span className="flex items-center">
              {isCompleted ? (
                <CheckCircle className="h-3.5 w-3.5 mr-1" />
              ) : (
                <AlertCircle className="h-3.5 w-3.5 mr-1" />
              )}
              {isCompleted ? "Completed" : "Pending"}
            </span>
          </Badge>
        </CardHeader>

        <CardContent className="pt-4">
          <div className="space-y-3">
            {/* Score */}
            <div className="flex justify-between items-center">
              <span className={cn(
                "text-sm font-medium",
                isDarkMode ? "text-gray-400" : "text-gray-500"
              )}>
                Score:
              </span>
              <span className={cn(
                "font-semibold",
                isCompleted
                  ? isDarkMode ? "text-green-400" : "text-green-600"
                  : isDarkMode ? "text-gray-300" : "text-gray-600"
              )}>
                {quiz.scored || 0}/{quiz.tasks ? quiz.tasks.length : 0} tasks
              </span>
            </div>

            {/* Date */}
            <div className="flex justify-between items-center">
              <span className={cn(
                "text-sm font-medium",
                isDarkMode ? "text-gray-400" : "text-gray-500"
              )}>
                Created:
              </span>
              <span className={cn(
                "text-sm",
                isDarkMode ? "text-gray-300" : "text-gray-600"
              )}>
                {formatDate(quiz.created_at)}
              </span>
            </div>

            {/* Tasks count */}
            <div className="flex justify-between items-center">
              <span className={cn(
                "text-sm font-medium",
                isDarkMode ? "text-gray-400" : "text-gray-500"
              )}>
                Tasks:
              </span>
              <span className={cn(
                "text-sm",
                isDarkMode ? "text-gray-300" : "text-gray-600"
              )}>
                {quiz.tasks ? quiz.tasks.length : 0} {quiz.tasks && quiz.tasks.length === 1 ? 'task' : 'tasks'}
              </span>
            </div>

            {/* Progress bar */}
            <div className="mt-2">
              <div className="flex justify-between items-center text-xs mb-1">
                <span className={cn(
                  isDarkMode ? "text-gray-400" : "text-gray-500"
                )}>
                  Progress
                </span>
                <span className={cn(
                  "font-medium",
                  calculateProgress() === 100
                    ? isDarkMode ? "text-green-400" : "text-green-600"
                    : calculateProgress() > 0
                      ? isDarkMode ? "text-blue-400" : "text-blue-600"
                      : isDarkMode ? "text-gray-400" : "text-gray-500"
                )}>
                  {calculateProgress()}%
                </span>
              </div>
              <Progress
                value={calculateProgress()}
                className={cn(
                  "h-1.5",
                  isDarkMode ? "bg-gray-700" : "bg-gray-200"
                )}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default QuizResultsCard;
