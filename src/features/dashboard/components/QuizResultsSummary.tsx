import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, AlertCircle, BarChart3 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTheme } from '@/core/hooks/useTheme';
import { motion } from 'framer-motion';

interface QuizResultsSummaryProps {
  quizzes: Array<{
    id?: string;
    _id?: string;
    status: string;
    scored?: number;
    max_score?: number;
    tasks: string[];
  }>;
}

/**
 * Summary component for quiz results showing statistics
 */
const QuizResultsSummary: React.FC<QuizResultsSummaryProps> = ({ quizzes }) => {
  const { isDarkMode } = useTheme();

  // Calculate summary statistics
  const totalQuizzes = quizzes.length;
  const completedQuizzes = quizzes.filter(quiz => quiz.status === 'completed').length;
  const pendingQuizzes = totalQuizzes - completedQuizzes;

  // Calculate completion percentage
  const completionPercentage = totalQuizzes > 0
    ? Math.round((completedQuizzes / totalQuizzes) * 100)
    : 0;

  // Calculate average score (only for completed quizzes)
  const completedQuizzesData = quizzes.filter(quiz => quiz.status === 'completed');
  const totalScore = completedQuizzesData.reduce((sum, quiz) => sum + (quiz.scored || 0), 0);
  const totalTasks = completedQuizzesData.reduce((sum, quiz) => sum + (quiz.tasks ? quiz.tasks.length : 0), 0);

  // Debug summary calculations
  console.log('Completed quizzes:', completedQuizzesData);
  console.log('Total score:', totalScore);
  console.log('Total tasks:', totalTasks);

  const averageScorePercentage = totalTasks > 0
    ? Math.round((totalScore / totalTasks) * 100)
    : 0;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="mb-6"
    >
      <Card className={cn(
        "overflow-hidden",
        isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
      )}>
        <CardHeader className={cn(
          "pb-2",
          isDarkMode ? "bg-blue-900/20" : "bg-blue-50"
        )}>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className={cn(
              "h-5 w-5",
              isDarkMode ? "text-blue-400" : "text-blue-600"
            )} />
            <span>Quiz Summary</span>
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Completion Status */}
            <div className="space-y-3">
              <h3 className={cn(
                "text-sm font-medium",
                isDarkMode ? "text-gray-300" : "text-gray-700"
              )}>
                Completion Status
              </h3>

              <div className="space-y-1">
                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-1.5">
                    <CheckCircle className={cn(
                      "h-4 w-4",
                      isDarkMode ? "text-green-400" : "text-green-600"
                    )} />
                    <span className={isDarkMode ? "text-gray-300" : "text-gray-700"}>
                      Completed
                    </span>
                  </span>
                  <span className="font-medium">
                    {completedQuizzes} ({completionPercentage}%)
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="flex items-center gap-1.5">
                    <AlertCircle className={cn(
                      "h-4 w-4",
                      isDarkMode ? "text-amber-400" : "text-amber-600"
                    )} />
                    <span className={isDarkMode ? "text-gray-300" : "text-gray-700"}>
                      Pending
                    </span>
                  </span>
                  <span className="font-medium">
                    {pendingQuizzes} ({100 - completionPercentage}%)
                  </span>
                </div>

                <Progress
                  value={completionPercentage}
                  className={cn(
                    "h-2 mt-2",
                    isDarkMode ? "bg-gray-700" : "bg-gray-200"
                  )}
                />
              </div>
            </div>

            {/* Average Score */}
            <div className="space-y-3">
              <h3 className={cn(
                "text-sm font-medium",
                isDarkMode ? "text-gray-300" : "text-gray-700"
              )}>
                Average Score
              </h3>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className={isDarkMode ? "text-gray-300" : "text-gray-700"}>
                    {completedQuizzes > 0 ? (
                      <>
                        {totalScore} / {totalTasks} tasks
                      </>
                    ) : (
                      'No completed quizzes'
                    )}
                  </span>
                  <span className={cn(
                    "font-medium",
                    isDarkMode ? "text-blue-400" : "text-blue-600"
                  )}>
                    {averageScorePercentage}%
                  </span>
                </div>

                <Progress
                  value={averageScorePercentage}
                  className={cn(
                    "h-2 mt-2",
                    isDarkMode ? "bg-gray-700" : "bg-gray-200"
                  )}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default QuizResultsSummary;
