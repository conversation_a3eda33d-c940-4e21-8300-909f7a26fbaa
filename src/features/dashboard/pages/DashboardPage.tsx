import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import MainLayout from '@/components/layout/MainLayout';
import AnimatedPage, { StaggeredChildren } from '@/components/common/AnimatedPage';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

/**
 * Enhanced dashboard page with animations and theme support
 */
const DashboardPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);

  // Animation variants
  const cardHoverVariants = {
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <MainLayout
      title="Dashboard"
      description={`Welcome back, ${user?.username}!`}
    >
      <AnimatedPage>
        {/* Quick Access Cards */}
        <div className="mb-8">
          <h2 className={cn(
            "text-xl font-semibold mb-4",
            isDarkMode ? "text-gray-200" : "text-gray-800"
          )}>
            Quick Access
          </h2>
          <StaggeredChildren className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Begin Learning Card */}
            <motion.div
              whileHover="hover"
              variants={cardHoverVariants}
            >
              <Card className={cn(
                "shadow-md hover:shadow-lg transition-shadow",
                isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
              )}>
                <CardHeader className={cn(
                  "pb-2",
                  isDarkMode ? "bg-red-900/20" : "bg-nepali-red bg-opacity-10"
                )}>
                  <CardTitle className={cn(
                    "text-xl",
                    isDarkMode ? "text-red-400" : "text-nepali-maroon"
                  )}>
                    Begin Learning
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <p className={cn(
                    isDarkMode ? "text-gray-300" : "text-gray-600"
                  )}>
                    Start your Nepali learning journey today. Practice speaking with our real-time audio recording.
                  </p>
                  <div className="mt-4">
                    <Button
                      className="nepali-gradient text-white"
                      onClick={() => navigate("/begin-learning")}
                    >
                      Start Now
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Tasks Card */}
            <motion.div
              whileHover="hover"
              variants={cardHoverVariants}
            >
              <Card className={cn(
                "shadow-md hover:shadow-lg transition-shadow",
                isDarkMode ? "bg-gray-800 border-gray-700" : "bg-white"
              )}>
                <CardHeader className={cn(
                  "pb-2",
                  isDarkMode ? "bg-blue-900/20" : "bg-blue-100"
                )}>
                  <CardTitle className={cn(
                    "text-xl",
                    isDarkMode ? "text-blue-400" : "text-blue-700"
                  )}>
                    Task Sets
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <p className={cn(
                    isDarkMode ? "text-gray-300" : "text-gray-600"
                  )}>
                    View and manage your task sets. Track your progress and complete assignments.
                  </p>
                  <div className="mt-4">
                    <Button
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      onClick={() => navigate("/tasks")}
                    >
                      View Tasks
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </StaggeredChildren>
        </div>

        {/* Additional content can be added here if needed */}
      </AnimatedPage>
    </MainLayout>
  );
};

export default DashboardPage;
