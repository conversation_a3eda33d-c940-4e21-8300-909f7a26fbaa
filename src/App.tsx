import React, { lazy, Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "./store/store";
import { AnimatePresence } from "framer-motion";

// Components
import ErrorBoundary from "@/components/ErrorBoundary";
import LoadingSpinner from "@/components/common/LoadingSpinner";

// Lazy-loaded Pages
const Index = lazy(() => import("./features/auth/pages/LandingPage"));
const AuthenticationPage = lazy(() => import("./features/auth/pages/AuthenticationPage"));
const GoogleAuthCallback = lazy(() => import("./features/auth/pages/GoogleAuthCallback"));
const OnboardingPage = lazy(() => import("./features/auth/pages/OnboardingPage"));
const DashboardPage = lazy(() => import("./features/dashboard/pages/DashboardPage"));
const BeginLearningPage = lazy(() => import("./features/learning/pages/BeginLearningPage"));
const TaskView = lazy(() => import("./features/learning/pages/TaskViewPage"));
const TaskPage = lazy(() => import("./features/tasks/pages/EnhancedTaskListPage"));
// StreamPage component removed - not implemented yet
// User management page removed
const NotFound = lazy(() => import("./features/core/pages/NotFoundPage"));
const TaskSetDetails = lazy(() => import("./features/tasks/pages/EnhancedTaskListPage"));

// Context Providers
import { AuthProvider } from "./context/AuthContext";
import { ApiProvider } from "./context/ApiContext";
import { ThemeProvider } from "./core/context/ThemeContext";

// Routes
import ProtectedRoute from "./routes/ProtectedRoute";

// Create Query Client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// Loading fallback component
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <LoadingSpinner size="lg" text="Loading page..." />
  </div>
);

// AnimatedRoutes component for page transitions
const AnimatedRoutes = () => {
  const location = useLocation();

  return (
    <AnimatePresence mode="wait" initial={false}>
      <Suspense fallback={<PageLoader />}>
        <Routes location={location} key={location.pathname}>
          {/* Public routes */}
          <Route path="/" element={<Index />} />
          <Route path="/:slug/login" element={<AuthenticationPage mode="login" />} />
          <Route path="/:slug/signup" element={<AuthenticationPage mode="signup" />} />
          <Route path="/auth/google/callback" element={<GoogleAuthCallback />} />

          {/* Onboarding route - protected but accessible to new users */}
          <Route
            path="/onboarding"
            element={
              <ProtectedRoute>
                <OnboardingPage />
              </ProtectedRoute>
            }
          />

          {/* Protected routes */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <DashboardPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/begin-learning"
            element={
              <ProtectedRoute>
                <BeginLearningPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/tasks/:taskSetId"
            element={
              <ProtectedRoute>
                <TaskView />
              </ProtectedRoute>
            }
          />

          <Route
            path="/tasks"
            element={
              <ProtectedRoute>
                <TaskPage />
              </ProtectedRoute>
            }
          />

          {/* Stream route removed - not implemented yet */}

          <Route
            path="/task-sets/:taskSetId"
            element={
              <ProtectedRoute>
                <TaskSetDetails />
              </ProtectedRoute>
            }
          />

          {/* User management route removed */}

          {/* Not found route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </Suspense>
    </AnimatePresence>
  );
};

const App: React.FC = () => (
  <ErrorBoundary>
    <ReduxProvider store={store}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <AuthProvider>
            <ApiProvider>
              <TooltipProvider>
                <Toaster />
                <Sonner />
                <BrowserRouter>
                  <AnimatedRoutes />
                </BrowserRouter>
              </TooltipProvider>
            </ApiProvider>
          </AuthProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ReduxProvider>
  </ErrorBoundary>
);

export default App;
