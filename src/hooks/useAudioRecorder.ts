import { useEffect, useRef, useState, useCallback } from 'react';

/**
 * Options for the useRecorder hook
 */
interface UseRecorderOptions {
  /** Callback function that receives audio chunks as they become available */
  onDataAvailable?: (chunk: Blob) => void;
  /** Callback function that is called when recording is stopped */
  onRecordingStopped?: () => void;
}

/**
 * Hook for recording audio with MediaRecorder API
 */
export const useRecorder = ({
  onDataAvailable,
  onRecordingStopped
}: UseRecorderOptions = {}) => {
  // State
  const [recording, setRecording] = useState<boolean>(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | undefined>();
  const [permissionGranted, setPermissionGranted] = useState<boolean>(false);

  // Refs
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  /**
   * Check if browser supports audio recording
   */
  const checkBrowserCompatibility = useCallback(() => {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }, []);

  /**
   * Get the best supported MIME type for audio recording
   */
  const getSupportedMimeType = useCallback(() => {
    const mimeTypes = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus',
      'audio/ogg',
      'audio/wav'
    ];

    for (const mimeType of mimeTypes) {
      if (MediaRecorder.isTypeSupported(mimeType)) {
        console.log('Using supported MIME type:', mimeType);
        return mimeType;
      }
    }

    // Fallback to empty string to let MediaRecorder choose
    console.warn('No explicitly supported MIME type found, using default');
    return '';
  }, []);

  /**
   * Start audio recording
   */
  const startRecording = useCallback(async () => {
    console.log('useRecorder: startRecording called');
    if (recording) {
      console.log('Already recording, ignoring startRecording call');
      return;
    }

    // Check compatibility
    if (!checkBrowserCompatibility()) {
      console.error('Browser compatibility check failed');
      setError("Your browser doesn't support audio recording");
      return;
    }

    // Reset state
    setAudioUrl(null);
    setError(null);
    audioChunksRef.current = [];
    console.log('State reset for new recording');

    try {
      console.log('Requesting microphone access with browser default device selection...');
      // Use browser's native device selection by not specifying deviceId
      // This will trigger the browser's permission dialog with device selection
      const constraints: MediaStreamConstraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1,
          sampleSize: 16
        }
      };

      console.log('Using audio constraints:', constraints);
      const mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('Microphone access granted');

      // Get the actual device being used
      const tracks = mediaStream.getAudioTracks();
      if (tracks.length > 0) {
        console.log('Using audio track:', tracks[0].label);
        const settings = tracks[0].getSettings();
        console.log('Actual audio settings:', settings);
        if (settings.deviceId) {
          setSelectedDeviceId(settings.deviceId);
        }
      }

      setStream(mediaStream);
      setPermissionGranted(true);

      // Get the best supported MIME type
      const mimeType = getSupportedMimeType();
      console.log('Using MIME type:', mimeType);

      // Create MediaRecorder with supported MIME type
      const mediaRecorderOptions: MediaRecorderOptions = {};
      if (mimeType) {
        mediaRecorderOptions.mimeType = mimeType;
      }

      // Set appropriate bit rate based on MIME type
      if (mimeType.includes('webm') || mimeType.includes('opus')) {
        mediaRecorderOptions.audioBitsPerSecond = 128000;
      } else {
        mediaRecorderOptions.audioBitsPerSecond = 256000;
      }

      const mediaRecorder = new MediaRecorder(mediaStream, mediaRecorderOptions);
      console.log('MediaRecorder created');

      // Set smaller chunk duration (200ms) for ~6400 bytes chunks
      const chunkDurationMs = 200;

      // Handle data available event
      mediaRecorder.ondataavailable = (event) => {
        console.log(`MediaRecorder ondataavailable event triggered`);

        if (event.data && event.data.size > 0) {
          console.log(`Audio chunk received: ${event.data.size} bytes, type: ${event.data.type}`);
          audioChunksRef.current.push(event.data);

          if (onDataAvailable) {
            console.log('Calling onDataAvailable callback with chunk');
            onDataAvailable(event.data);
          }
        } else {
          console.warn('Empty audio chunk received or no data in event');
        }
      };

      // Add additional event listeners for debugging
      mediaRecorder.onstart = () => {
        console.log('MediaRecorder started');
      };

      mediaRecorder.onpause = () => {
        console.log('MediaRecorder paused');
      };

      mediaRecorder.onresume = () => {
        console.log('MediaRecorder resumed');
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        console.log('MediaRecorder stopped');
        if (audioChunksRef.current.length === 0) {
          console.error('No audio data recorded');
          setError("No audio data recorded");
          return;
        }

        // Use the actual MIME type from the MediaRecorder
        const actualMimeType = mediaRecorder.mimeType || mimeType || 'audio/webm';
        const audioBlob = new Blob(audioChunksRef.current, { type: actualMimeType });
        console.log(`Created audio blob: ${audioBlob.size} bytes, type: ${actualMimeType}`);
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
      };

      // Handle recording errors
      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event.error);
        setError(`Recording error: ${event.error}`);
        stopRecording();
      };

      // Start recording with configurable chunk duration
      console.log(`Starting MediaRecorder with ${chunkDurationMs}ms chunks`);
      console.log(`MediaRecorder configuration: mimeType=${mimeType}, audioBitsPerSecond=128000`);

      // Store the recorder reference before starting
      mediaRecorderRef.current = mediaRecorder;

      // Start the recorder with the specified chunk duration
      mediaRecorder.start(chunkDurationMs);

      // Update state
      setRecording(true);
      console.log('Recording started successfully');

      // Log the MediaRecorder state
      console.log(`MediaRecorder state: ${mediaRecorder.state}`);
      console.log(`MediaRecorder mimeType: ${mediaRecorder.mimeType}`);
    } catch (err) {
      console.error('Error starting recording:', err);
      setError(err.message || 'Failed to start recording');
    }
  }, [recording, checkBrowserCompatibility, onDataAvailable, getSupportedMimeType]);

  /**
   * Stop audio recording
   */
  const stopRecording = useCallback(() => {
    if (!recording || !mediaRecorderRef.current) return;

    try {
      console.log('useRecorder: stopRecording called');

      // Request final data chunk
      if (mediaRecorderRef.current.state === 'recording') {
        try {
          console.log('Requesting final data chunk from MediaRecorder');
          mediaRecorderRef.current.requestData();

          // Give a moment for the data to be processed
          console.log('Waiting for final data chunk to be processed');
        } catch (err) {
          console.error('Error requesting final data chunk:', err);
          setError('Error requesting final data chunk');
        }
      }

      // Stop recorder after a small delay to ensure the final chunk is processed
      setTimeout(() => {
        console.log('Stopping MediaRecorder after delay');
        if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
          mediaRecorderRef.current.stop();
          console.log('MediaRecorder stopped');
        } else {
          console.log('MediaRecorder already inactive or null');
        }

        setRecording(false);

        // Stop audio tracks
        if (stream) {
          console.log('Stopping audio tracks');
          stream.getTracks().forEach(track => {
            track.stop();
            console.log(`Audio track ${track.id} stopped`);
          });
          setStream(null);
        }

        // Call the onRecordingStopped callback if provided
        if (onRecordingStopped) {
          console.log('Calling onRecordingStopped callback');
          onRecordingStopped();
        } else {
          console.log('No onRecordingStopped callback provided');
        }
      }, 300); // Increased delay to ensure final chunk is processed
    } catch (err) {
      console.error('Error in stopRecording:', err);
      setError(err.message || 'Failed to stop recording');
    }
  }, [recording, stream, onRecordingStopped]);

  /**
   * Cancel audio recording
   */
  const cancelRecording = useCallback(() => {
    if (!recording || !mediaRecorderRef.current) return;

    try {
      // Stop recorder
      if (mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
      setRecording(false);

      // Clear state
      setAudioUrl(null);
      audioChunksRef.current = [];

      // Stop audio tracks
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
        setStream(null);
      }
    } catch (err) {
      setError(err.message || 'Failed to cancel recording');
    }
  }, [recording, stream]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Stop audio tracks
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      // Stop recorder
      if (mediaRecorderRef.current && recording) {
        try {
          if (mediaRecorderRef.current.state !== 'inactive') {
            mediaRecorderRef.current.stop();
          }
        } catch (err) {
          // Ignore cleanup errors
        }
      }

      // Release object URL
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [stream, recording, audioUrl]);

  return {
    recording,
    audioUrl,
    error,
    startRecording,
    stopRecording,
    cancelRecording,
    selectedDeviceId,
    permissionGranted
  };
};
