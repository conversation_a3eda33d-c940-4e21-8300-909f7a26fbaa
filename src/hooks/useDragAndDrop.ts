import { useState, useRef, useEffect } from 'react';

interface DraggableItem {
  id: string;
  [key: string]: any;
}

/**
 * Custom hook for drag and drop functionality
 */
export const useDragAndDrop = <T extends DraggableItem>(initialItems: T[]) => {
  const [items, setItems] = useState<T[]>(initialItems);
  const [draggedItem, setDraggedItem] = useState<T | null>(null);
  const [draggedOverItem, setDraggedOverItem] = useState<T | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  
  // Update items when initialItems change
  useEffect(() => {
    setItems(initialItems);
  }, [initialItems]);
  
  /**
   * Handle drag start
   * @param item The item being dragged
   */
  const handleDragStart = (item: T) => {
    setDraggedItem(item);
    setIsDragging(true);
  };
  
  /**
   * Handle drag over
   * @param item The item being dragged over
   */
  const handleDragOver = (item: T) => {
    if (draggedItem && draggedItem.id !== item.id) {
      setDraggedOverItem(item);
    }
  };
  
  /**
   * Handle drag end
   */
  const handleDragEnd = () => {
    if (draggedItem && draggedOverItem) {
      // Reorder items
      const newItems = [...items];
      const draggedIndex = newItems.findIndex(item => item.id === draggedItem.id);
      const draggedOverIndex = newItems.findIndex(item => item.id === draggedOverItem.id);
      
      // Remove dragged item
      const [removed] = newItems.splice(draggedIndex, 1);
      
      // Insert at new position
      newItems.splice(draggedOverIndex, 0, removed);
      
      setItems(newItems);
    }
    
    // Reset drag state
    setDraggedItem(null);
    setDraggedOverItem(null);
    setIsDragging(false);
  };
  
  /**
   * Handle drop
   */
  const handleDrop = () => {
    handleDragEnd();
  };
  
  /**
   * Check if an item is being dragged
   * @param item The item to check
   * @returns True if the item is being dragged
   */
  const isItemDragged = (item: T) => {
    return draggedItem?.id === item.id;
  };
  
  /**
   * Check if an item is being dragged over
   * @param item The item to check
   * @returns True if the item is being dragged over
   */
  const isItemDraggedOver = (item: T) => {
    return draggedOverItem?.id === item.id;
  };
  
  return {
    items,
    setItems,
    isDragging,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
    handleDrop,
    isItemDragged,
    isItemDraggedOver
  };
};
