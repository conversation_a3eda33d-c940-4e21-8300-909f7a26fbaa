import { useState, useEffect } from 'react';
import { AnimationControls, useAnimation } from 'framer-motion';

/**
 * Custom hook for managing animations
 */
export const useAnimations = () => {
  const controls = useAnimation();
  
  /**
   * Animate an element with a fade-in effect
   */
  const animateFadeIn = async () => {
    await controls.start({
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: 'easeOut' }
    });
  };
  
  /**
   * Animate an element with a fade-out effect
   */
  const animateFadeOut = async () => {
    await controls.start({
      opacity: 0,
      y: 20,
      transition: { duration: 0.3, ease: 'easeIn' }
    });
  };
  
  /**
   * Animate an element with a scale effect (for buttons, etc.)
   */
  const animateScale = async () => {
    await controls.start({
      scale: [1, 1.05, 1],
      transition: { duration: 0.3, ease: 'easeInOut' }
    });
  };
  
  /**
   * Animate an element with a success effect (for confirmations)
   */
  const animateSuccess = async () => {
    await controls.start({
      scale: [1, 1.1, 1],
      backgroundColor: ['#ffffff', '#ecfdf5', '#ffffff'],
      transition: { duration: 0.5, ease: 'easeInOut' }
    });
  };
  
  /**
   * Animate an element with a shake effect (for errors)
   */
  const animateShake = async () => {
    await controls.start({
      x: [0, -10, 10, -10, 10, 0],
      transition: { duration: 0.5, ease: 'easeInOut' }
    });
  };
  
  /**
   * Animate a page transition
   */
  const animatePageTransition = async (direction: 'in' | 'out') => {
    if (direction === 'in') {
      await controls.start({
        opacity: 1,
        x: 0,
        transition: { duration: 0.5, ease: 'easeOut' }
      });
    } else {
      await controls.start({
        opacity: 0,
        x: -50,
        transition: { duration: 0.3, ease: 'easeIn' }
      });
    }
  };
  
  /**
   * Animate a tab switch
   */
  const animateTabSwitch = async (direction: 'left' | 'right') => {
    const xValue = direction === 'left' ? -20 : 20;
    
    await controls.start({
      opacity: 0,
      x: xValue,
      transition: { duration: 0.2, ease: 'easeIn' }
    });
    
    await controls.start({
      opacity: 1,
      x: 0,
      transition: { duration: 0.3, ease: 'easeOut' }
    });
  };
  
  return {
    controls,
    animateFadeIn,
    animateFadeOut,
    animateScale,
    animateSuccess,
    animateShake,
    animatePageTransition,
    animateTabSwitch
  };
};

/**
 * Custom hook for staggered animations of list items
 */
export const useStaggeredAnimation = (itemCount: number) => {
  const controls = useAnimation();
  
  const animateItems = async () => {
    await controls.start(i => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.3,
        ease: 'easeOut'
      }
    }));
  };
  
  const resetItems = async () => {
    await controls.start({
      opacity: 0,
      y: 20,
      transition: { duration: 0.2 }
    });
  };
  
  return {
    controls,
    animateItems,
    resetItems
  };
};

/**
 * Custom hook for skeleton loading animation
 */
export const useSkeletonLoading = (isLoading: boolean) => {
  const [showSkeleton, setShowSkeleton] = useState(isLoading);
  
  useEffect(() => {
    if (isLoading) {
      setShowSkeleton(true);
    } else {
      // Add a small delay before hiding skeleton for smoother transition
      const timer = setTimeout(() => {
        setShowSkeleton(false);
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [isLoading]);
  
  return { showSkeleton };
};

/**
 * Custom hook for success confirmation animation
 */
export const useSuccessAnimation = () => {
  const [showSuccess, setShowSuccess] = useState(false);
  const controls = useAnimation();
  
  const triggerSuccess = async () => {
    setShowSuccess(true);
    
    await controls.start({
      scale: [1, 1.1, 1],
      opacity: [0, 1, 1],
      transition: { duration: 0.5 }
    });
    
    // Hide success animation after 2 seconds
    setTimeout(() => {
      controls.start({
        opacity: 0,
        transition: { duration: 0.3 }
      }).then(() => {
        setShowSuccess(false);
      });
    }, 2000);
  };
  
  return {
    showSuccess,
    successControls: controls,
    triggerSuccess
  };
};
