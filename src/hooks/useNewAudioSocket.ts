/**
 * New Audio Socket.IO Hook
 * Implements the complete audio recording and transmission flow
 * following the provided API specification
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { newSocketClient } from '../api/newSocketClient';
import { useRecorder } from './useAudioRecorder';
import type {
  UseNewAudioSocketOptions,
  AudioSocketState,
  AudioFormat,
  BinaryDataEventPayload,
  ChunkReceivedEventPayload,
  TaskGenerationFailedEventPayload,
  QueueStatusEventPayload,
  ErrorEventPayload
} from '../types/socketTypes';

/**
 * Default audio format configuration following API specification
 */
const DEFAULT_AUDIO_FORMAT: AudioFormat = {
  format: 'webm/opus',
  sampleRate: 16000,
  channels: 1,
  chunkDuration: 1, // 1 second chunks
  maxChunkSize: 1048576 // 1MB
};

/**
 * New Audio Socket.IO Hook Implementation
 */
export function useNewAudioSocket(options: UseNewAudioSocketOptions) {
  const {
    onStateChange,
    onChunkReceived,
    onTaskGenerated,
    onTaskGenerationFailed,
    onQueueStatus,
    onError,
    audioFormat = {}
  } = options;

  // Merge audio format with defaults
  const finalAudioFormat: AudioFormat = { ...DEFAULT_AUDIO_FORMAT, ...audioFormat };

  // State management
  const [state, setState] = useState<AudioSocketState>({
    connectionState: 'DISCONNECTED',
    isRecording: false,
    sessionId: null,
    chunksSent: 0,
    totalBytesSent: 0,
    chunksAcknowledged: 0,
    totalBytesAcknowledged: 0,
    queuePosition: null,
    estimatedWaitTime: null,
    taskSetId: null,
    lastError: null
  });

  // Refs for tracking
  const chunkSequenceRef = useRef<number>(0);
  const pendingChunksRef = useRef<Set<number>>(new Set());
  const recordingStartTimeRef = useRef<number | null>(null);
  const isRecordingRef = useRef<boolean>(false); // Immediate recording state tracking

  /**
   * Update state and trigger callbacks
   */
  const updateState = useCallback((updates: Partial<AudioSocketState>) => {
    setState(prevState => {
      const newState = { ...prevState, ...updates };

      // Trigger state change callback if connection state changed
      if (updates.connectionState && updates.connectionState !== prevState.connectionState) {
        onStateChange?.(updates.connectionState);
      }

      return newState;
    });
  }, [onStateChange]);

  /**
   * Handle audio chunks from recorder - simplified flow with enhanced debugging
   */
  const handleAudioChunk = useCallback(async (chunk: Blob) => {
    if (!chunk || chunk.size === 0) {
      console.warn('Received empty audio chunk, ignoring');
      return;
    }

    // Enhanced debugging for state synchronization issues
    const isConnected = newSocketClient.isConnected();
    const stateRecording = state.isRecording;
    const refRecording = isRecordingRef.current;

    console.log('🔍 handleAudioChunk called:', {
      chunkSize: chunk.size,
      'state.isRecording': stateRecording,
      'isRecordingRef.current': refRecording,
      'newSocketClient.isConnected()': isConnected,
      sessionId: state.sessionId
    });

    // Use ref for immediate state check (avoids React state update timing issues)
    if (!refRecording || !isConnected) {
      console.warn('❌ Cannot send chunk - not recording or not connected:', {
        'isRecordingRef.current': refRecording,
        'newSocketClient.isConnected()': isConnected,
        'state.isRecording': stateRecording
      });
      return;
    }

    try {
      // Increment chunk sequence
      chunkSequenceRef.current += 1;
      const chunkNumber = chunkSequenceRef.current;

      // Convert blob to ArrayBuffer
      const arrayBuffer = await chunk.arrayBuffer();

      // Validate chunk size
      if (arrayBuffer.byteLength > finalAudioFormat.maxChunkSize) {
        console.warn(`Chunk #${chunkNumber} exceeds max size: ${arrayBuffer.byteLength} bytes`);
        return;
      }

      console.log(`📤 Sending audio chunk #${chunkNumber}: ${arrayBuffer.byteLength} bytes`);

      // Create binary data payload following new specification
      const audioData = new Uint8Array(arrayBuffer);
      const currentSessionId = state.sessionId || newSocketClient.getSessionId();

      console.log('🔍 Session ID for audio chunk:', {
        'state.sessionId': state.sessionId,
        'newSocketClient.getSessionId()': newSocketClient.getSessionId(),
        'currentSessionId (using)': currentSessionId
      });

      if (!currentSessionId) {
        console.error('❌ No session ID available for sending audio chunk');
        return;
      }

      const binaryPayload: BinaryDataEventPayload = {
        session_id: currentSessionId,
        chunk_id: Date.now(), // Use timestamp as chunk ID
        audio_data: audioData,
        metadata: {
          timestamp: new Date().toISOString(),
          chunk_size: audioData.length,
          sequence_number: chunkNumber
        }
      };

      // Send binary data
      await newSocketClient.sendBinaryData(binaryPayload);

      // Track pending chunk
      pendingChunksRef.current.add(chunkNumber);

      // Update state
      updateState({
        chunksSent: chunkSequenceRef.current,
        totalBytesSent: state.totalBytesSent + arrayBuffer.byteLength
      });

    } catch (error) {
      console.error('Error sending audio chunk:', error);
      const errorPayload: ErrorEventPayload = {
        message: `Failed to send audio chunk: ${error.message}`,
        code: 'CHUNK_SEND_ERROR',
        timestamp: new Date().toISOString()
      };
      updateState({ lastError: errorPayload });
      onError?.(errorPayload);
    }
  }, [state.totalBytesSent, finalAudioFormat.maxChunkSize, updateState, onError]);

  /**
   * Initialize audio recorder with proper configuration
   */
  const {
    recording,
    audioUrl,
    error: recorderError,
    startRecording: startRecorderHook,
    stopRecording: stopRecorderHook,
    cancelRecording: cancelRecorderHook,
    availableDevices,
    selectedDeviceId,
    setAudioDevice,
    getAudioDevices,
    permissionGranted
  } = useRecorder({
    onDataAvailable: handleAudioChunk,
    chunkDurationMs: finalAudioFormat.chunkDuration * 1000, // Convert to milliseconds
    onRecordingStopped: () => {
      console.log('🛑 Audio recorder stopped');
    }
  });

  /**
   * Connect to Socket.IO server
   */
  const connect = useCallback(async () => {
    try {
      console.log('🔌 Connecting to Socket.IO server...');
      updateState({ connectionState: 'DISCONNECTED', lastError: null });

      console.log('🔍 Debug - About to call newSocketClient.connect()');
      await newSocketClient.connect();
      console.log('🔍 Debug - newSocketClient.connect() completed');

      const sessionId = newSocketClient.getSessionId?.() || null;
      console.log('🔍 Debug - Retrieved session ID:', sessionId);

      const isConnected = newSocketClient.isConnected();
      console.log('🔍 Debug - Socket.IO isConnected():', isConnected);

      if (!isConnected) {
        throw new Error('Socket.IO connection not established');
      }

      updateState({
        connectionState: 'CONNECTED',
        sessionId
      });

      console.log('✅ Connected to Socket.IO server, session ID:', sessionId);

      // Give a small delay to ensure state is updated
      await new Promise(resolve => setTimeout(resolve, 100));

      return true;
    } catch (error) {
      console.error('❌ Failed to connect:', error);
      console.error('❌ Error details:', error.message, error.stack);
      const errorPayload: ErrorEventPayload = {
        message: `Connection failed: ${error.message}`,
        code: 'CONNECTION_ERROR',
        timestamp: new Date().toISOString()
      };
      updateState({
        connectionState: 'ERROR',
        lastError: errorPayload
      });
      onError?.(errorPayload);
      return false;
    }
  }, [updateState, onError]);

  /**
   * Disconnect from Socket.IO server
   */
  const disconnect = useCallback(() => {
    console.log('🔌 Disconnecting from Socket.IO server');
    newSocketClient.disconnect();
    updateState({
      connectionState: 'DISCONNECTED',
      sessionId: null,
      chunksSent: 0,
      totalBytesSent: 0,
      chunksAcknowledged: 0,
      totalBytesAcknowledged: 0,
      queuePosition: null,
      estimatedWaitTime: null,
      taskSetId: null
    });
  }, [updateState]);

  /**
   * Start audio recording and streaming - simplified flow:
   * 1. Ensure connection
   * 2. Send stream_starting event and get session_id
   * 3. Start recording - chunks will be sent immediately
   */
  const startRecording = useCallback(async () => {
    try {
      console.log('🎵 Starting audio recording and streaming...');

      // Ensure we're connected
      if (!newSocketClient.isConnected()) {
        console.log('Socket not connected, attempting to connect first...');
        const connected = await connect();
        if (!connected) {
          throw new Error('Failed to connect to server');
        }
      }

      // Reset tracking variables
      chunkSequenceRef.current = 0;
      pendingChunksRef.current.clear();
      recordingStartTimeRef.current = Date.now();

      console.log('🔄 Sending stream_starting event to server...');

      // Send stream_starting event (uses session_id from HTTP response internally)
      const confirmedSessionId = await newSocketClient.sendStreamStarting();
      console.log('✅ Received stream_starting_ack, confirmed session_id:', confirmedSessionId);

      // Set recording state immediately in ref (for immediate chunk handling)
      isRecordingRef.current = true;
      console.log('🔄 Set isRecordingRef.current = true');

      // Update state with confirmed session_id and recording status
      updateState({
        sessionId: confirmedSessionId,
        isRecording: true
      });

      // Start audio recording - chunks will be sent immediately via handleAudioChunk
      console.log('🔄 Starting audio recording...');
      await startRecorderHook();

      console.log('✅ Audio recording started - ready to send audio chunks');
      return true;
    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      // Reset recording state on error
      isRecordingRef.current = false;
      const errorPayload: ErrorEventPayload = {
        message: `Failed to start recording: ${error.message}`,
        code: 'RECORDING_START_ERROR',
        timestamp: new Date().toISOString()
      };
      updateState({ lastError: errorPayload });
      onError?.(errorPayload);
      return false;
    }
  }, [connect, startRecorderHook, updateState, onError]);

  /**
   * Stop audio recording and send completion
   */
  const stopRecording = useCallback(async () => {
    try {
      console.log('🛑 Stopping audio recording...');

      // Set recording state immediately in ref
      isRecordingRef.current = false;
      console.log('🔄 Set isRecordingRef.current = false');

      // Stop audio recorder first
      stopRecorderHook();

      // Wait a moment for final chunks to be processed
      await new Promise(resolve => setTimeout(resolve, 500));

      // Send stream completed event using simplified flow
      await newSocketClient.sendStreamCompleted();

      updateState({
        isRecording: false,
        connectionState: 'COMPLETED'
      });

      console.log('✅ Recording stopped and completion sent');
      return true;
    } catch (error) {
      console.error('❌ Failed to stop recording:', error);
      const errorPayload: ErrorEventPayload = {
        message: `Failed to stop recording: ${error.message}`,
        code: 'RECORDING_STOP_ERROR',
        timestamp: new Date().toISOString()
      };
      updateState({ lastError: errorPayload });
      onError?.(errorPayload);
      return false;
    }
  }, [stopRecorderHook, state.totalBytesSent, state.chunksAcknowledged, updateState, onError]);

  /**
   * Cancel audio recording
   */
  const cancelRecording = useCallback(async () => {
    try {
      console.log('❌ Cancelling audio recording');

      // Set recording state immediately in ref
      isRecordingRef.current = false;
      console.log('🔄 Set isRecordingRef.current = false');

      cancelRecorderHook();

      // Send stream stop event using simplified flow
      await newSocketClient.sendStreamStop();

      updateState({
        isRecording: false,
        connectionState: 'CANCELLED'
      });

      // Reset tracking
      chunkSequenceRef.current = 0;
      pendingChunksRef.current.clear();
      recordingStartTimeRef.current = null;
    } catch (error) {
      console.error('❌ Failed to cancel recording:', error);
      // Still update state even if cancellation message fails
      isRecordingRef.current = false;
      updateState({
        isRecording: false,
        connectionState: 'CANCELLED'
      });
    }
  }, [cancelRecorderHook, state.sessionId, updateState]);

  // Set up Socket.IO event listeners
  useEffect(() => {
    const handleChunkReceived = (data: ChunkReceivedEventPayload) => {
      console.log(`✅ Chunk #${data.chunk_number} acknowledged`);

      // Remove from pending chunks
      pendingChunksRef.current.delete(data.chunk_number);

      updateState({
        chunksAcknowledged: data.chunk_number,
        totalBytesAcknowledged: data.total_bytes_received
      });

      onChunkReceived?.(data);
    };



    const handleQueueStatus = (data: QueueStatusEventPayload) => {
      console.log('⏳ Queue status:', data);
      updateState({
        queuePosition: data.position,
        estimatedWaitTime: data.estimated_wait_time
      });
      onQueueStatus?.(data);
    };

    const handleError = (data: ErrorEventPayload) => {
      console.error('❌ Server error:', data);
      updateState({
        lastError: data,
        connectionState: 'ERROR'
      });
      onError?.(data);
    };

    const handleConnectAck = (data: any) => {
      console.log('✅ Connection acknowledged:', data);
      // Connection established, no state update needed
    };



    const handleStreamCompletedAck = (data: any) => {
      console.log('📡 Received stream_completed_ack:', data);
      console.log('✅ Stream completion acknowledged');
      updateState({ connectionState: 'COMPLETED' });
    };

    const handleStreamStopAck = (data: any) => {
      console.log('📡 Received stream_stop_ack:', data);
      console.log('✅ Stream stop acknowledged');
      updateState({ connectionState: 'CANCELLED' });
    };

    const handleTaskGenerationProcessing = (data: any) => {
      console.log('📡 Received task_generation_processing:', data);
      console.log('🔄 Processing audio for task generation');
    };

    const handleTaskGenerationComplete = (data: any) => {
      console.log('📡 Received task_generation_complete:', data);
      console.log('✅ Task generation completed successfully');
      updateState({
        connectionState: 'COMPLETED',
        taskSetId: data.session_id
      });
      onTaskGenerated?.(data);
    };

    const handleTaskGenerationFailed = (data: TaskGenerationFailedEventPayload) => {
      console.error('📡 Received task_generation_failed:', data);
      console.error('❌ Task generation failed:', data.message);
      updateState({
        connectionState: 'ERROR',
        lastError: {
          message: data.message,
          code: 'TASK_GENERATION_FAILED',
          timestamp: data.timestamp
        }
      });
      onTaskGenerationFailed?.(data);
    };

    const handleTaskGenerationCancelled = (data: any) => {
      console.log('📡 Received task_generation_cancelled:', data);
      console.log('❌ Task generation cancelled');
    };

    const handleStreamError = (data: any) => {
      console.error('📡 Received stream_error:', data);
      console.error('❌ Stream error:', data.reason, data.message);
      const errorPayload = {
        message: data.message || 'Stream error',
        code: data.reason || 'STREAM_ERROR',
        timestamp: data.timestamp || new Date().toISOString()
      };
      updateState({
        lastError: errorPayload,
        connectionState: 'ERROR'
      });
      onError?.(errorPayload);
    };



    // Register event listeners for simplified Socket.IO events
    newSocketClient.on('connect_ack', handleConnectAck);
    newSocketClient.on('stream_completed_ack', handleStreamCompletedAck);
    newSocketClient.on('stream_stop_ack', handleStreamStopAck);
    newSocketClient.on('task_generation_processing', handleTaskGenerationProcessing);
    newSocketClient.on('task_generation_complete', handleTaskGenerationComplete);
    newSocketClient.on('task_generation_failed', handleTaskGenerationFailed);
    newSocketClient.on('task_generation_cancelled', handleTaskGenerationCancelled);
    newSocketClient.on('stream_error', handleStreamError);
    newSocketClient.on('chunk_received', handleChunkReceived);
    newSocketClient.on('queue_status', handleQueueStatus);
    newSocketClient.on('error', handleError);

    // Cleanup on unmount
    return () => {
      newSocketClient.off('connect_ack', handleConnectAck);
      newSocketClient.off('stream_completed_ack', handleStreamCompletedAck);
      newSocketClient.off('stream_stop_ack', handleStreamStopAck);
      newSocketClient.off('task_generation_processing', handleTaskGenerationProcessing);
      newSocketClient.off('task_generation_complete', handleTaskGenerationComplete);
      newSocketClient.off('task_generation_failed', handleTaskGenerationFailed);
      newSocketClient.off('task_generation_cancelled', handleTaskGenerationCancelled);
      newSocketClient.off('stream_error', handleStreamError);
      newSocketClient.off('chunk_received', handleChunkReceived);
      newSocketClient.off('queue_status', handleQueueStatus);
      newSocketClient.off('error', handleError);
    };
  }, [updateState, onChunkReceived, onTaskGenerated, onTaskGenerationFailed, onQueueStatus, onError]);

  // Return hook API
  return {
    // State
    ...state,
    recording,
    audioUrl,
    error: recorderError || state.lastError,

    // Connection methods
    connect,
    disconnect,

    // Recording methods
    startRecording,
    stopRecording,
    cancelRecording,

    // Device selection (from useRecorder)
    availableDevices,
    selectedDeviceId,
    setAudioDevice,
    getAudioDevices,
    permissionGranted,

    // Computed values
    isConnected: newSocketClient.isConnected(),
    pendingChunks: pendingChunksRef.current.size,
    recordingDuration: recordingStartTimeRef.current
      ? (Date.now() - recordingStartTimeRef.current) / 1000
      : 0
  };
}
