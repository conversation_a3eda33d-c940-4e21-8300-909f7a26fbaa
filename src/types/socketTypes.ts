/**
 * TypeScript types for the new Socket.IO API implementation
 * Following the provided API specification
 */

// HTTP Authentication Types
export interface SocketAuthRequest {
  /** Optional metadata for the connection */
  metadata?: Record<string, any>;
}

export interface SocketAuthResponse {
  /** Session token for WebSocket authentication */
  session_token: string;
  /** Unique session identifier */
  session_id: string;
  /** WebSocket URL to connect to */
  websocket_url: string;
  /** Session expiration timestamp */
  expires_at: string;
  /** Configuration object */
  configuration: {
    difficulty: string;
    num_tasks: number;
    chunk_threshold: number;
  };
  /** Status of the session */
  status: string;
  /** Instructions for next steps */
  instructions: {
    next_step: string;
    websocket_endpoint: string;
    auth_method: string;
    events: {
      start_streaming: string;
      binary_data: string;
      recording_completed: string;
      recording_cancelled: string;
    };
  };
}

export interface SocketAuthError {
  /** Error message */
  message: string;
  /** Error code */
  code?: string;
  /** Additional error details */
  details?: Record<string, any>;
}

// Socket.IO Connection States
export type SocketConnectionState =
  | 'DISCONNECTED'
  | 'CONNECTED'
  | 'STARTED'
  | 'ACTIVE'
  | 'COMPLETED'
  | 'CANCELLED'
  | 'ERROR';

// Audio Format Requirements
export interface AudioFormat {
  /** Audio format - must be WebM/Opus */
  format: 'webm/opus';
  /** Sample rate - must be 16000Hz */
  sampleRate: 16000;
  /** Number of channels - must be mono (1) */
  channels: 1;
  /** Chunk duration in seconds - must be 1 second */
  chunkDuration: 1;
  /** Maximum chunk size in bytes - must be 1MB */
  maxChunkSize: 1048576; // 1MB
}

// Event Payloads
export interface ConnectedEventPayload {
  /** Session ID */
  session_id: string;
  /** Connection timestamp */
  timestamp: string;
  /** Server information */
  server_info?: Record<string, any>;
}

export interface StreamingStartedEventPayload {
  /** Session ID */
  session_id: string;
  /** Streaming start timestamp */
  timestamp: string;
  /** Audio format configuration */
  audio_format: AudioFormat;
}

export interface ChunkReceivedEventPayload {
  /** Chunk sequence number */
  chunk_number: number;
  /** Number of bytes received */
  bytes_received: number;
  /** Total bytes received so far */
  total_bytes_received: number;
  /** Acknowledgment timestamp */
  timestamp: string;
}

export interface TaskGeneratedEventPayload {
  /** Generated task set ID */
  task_set_id: string;
  /** Number of tasks generated */
  task_count: number;
  /** Generation timestamp */
  timestamp: string;
  /** Additional task metadata */
  metadata?: Record<string, any>;
}

export interface TaskGenerationFailedEventPayload {
  /** Session ID */
  session_id: string;
  /** Error message */
  message: string;
  /** Failure timestamp */
  timestamp: string;
  /** Empty tasks array */
  tasks: any[];
  /** Task count (should be 0) */
  task_count: number;
  /** Status (should be "failed") */
  status: string;
  /** Error details */
  error: string;
}

export interface QueueStatusEventPayload {
  /** Current queue position */
  position: number;
  /** Estimated wait time in seconds */
  estimated_wait_time: number;
  /** Queue status timestamp */
  timestamp: string;
}

export interface StatusUpdateEventPayload {
  /** Session ID */
  session_id: string;
  /** Status value */
  status: 'STARTED' | 'ACTIVE' | 'PAUSED' | 'COMPLETED' | 'CANCELLED' | 'ERROR';
  /** Status timestamp */
  timestamp: string;
  /** Optional metadata */
  metadata?: Record<string, any>;
}

export interface StatusConfirmedEventPayload {
  /** Session ID */
  session_id: string;
  /** Confirmed status */
  status: string;
  /** Update timestamp */
  updated_at: string;
}

export interface ServerStatusEventPayload {
  /** Queue position */
  queue_position?: number;
  /** Processing status */
  processing_status: string;
  /** Estimated wait time */
  estimated_wait?: string;
}

export interface ProcessingStatusEventPayload {
  /** Session ID */
  session_id: string;
  /** Number of chunks processed */
  chunks_processed: number;
  /** Number of chunks pending */
  chunks_pending: number;
  /** Number of tasks generated */
  tasks_generated: number;
  /** Processing time */
  processing_time: string;
}

export interface ErrorEventPayload {
  /** Error message */
  message: string;
  /** Error code */
  code: string;
  /** Error timestamp */
  timestamp: string;
  /** Additional error details */
  details?: Record<string, any>;
}

// Client Event Payloads
export interface StartStreamingEventPayload {
  /** Audio format specification */
  audio_format: AudioFormat;
  /** Streaming start timestamp */
  timestamp: string;
  /** Optional metadata */
  metadata?: Record<string, any>;
}

export interface BinaryDataEventPayload {
  /** Session ID */
  session_id: string;
  /** Chunk ID */
  chunk_id: number;
  /** Audio data as Uint8Array */
  audio_data: Uint8Array;
  /** Metadata */
  metadata: {
    timestamp: string;
    chunk_size: number;
    sequence_number: number;
  };
}

export interface RecordingCompletedEventPayload {
  /** Session ID */
  session_id: string;
  /** Total number of chunks sent */
  total_chunks: number;
  /** Total recording duration */
  total_duration: string;
  /** Optional metadata */
  metadata?: {
    recording_quality?: string;
    completion_reason?: string;
  };
}

// Hook Options and State
export interface UseNewAudioSocketOptions {
  /** Authentication token */
  token: string | null;
  /** Callback for connection state changes */
  onStateChange?: (state: SocketConnectionState) => void;
  /** Callback for chunk acknowledgments */
  onChunkReceived?: (data: ChunkReceivedEventPayload) => void;
  /** Callback for task generation */
  onTaskGenerated?: (data: TaskGeneratedEventPayload) => void;
  /** Callback for task generation failure */
  onTaskGenerationFailed?: (data: TaskGenerationFailedEventPayload) => void;
  /** Callback for queue status updates */
  onQueueStatus?: (data: QueueStatusEventPayload) => void;
  /** Callback for errors */
  onError?: (error: ErrorEventPayload) => void;
  /** Audio format configuration (optional, uses defaults) */
  audioFormat?: Partial<AudioFormat>;
}

export interface AudioSocketState {
  /** Current connection state */
  connectionState: SocketConnectionState;
  /** Whether currently recording */
  isRecording: boolean;
  /** Current session ID */
  sessionId: string | null;
  /** Number of chunks sent */
  chunksSent: number;
  /** Total bytes sent */
  totalBytesSent: number;
  /** Number of chunks acknowledged */
  chunksAcknowledged: number;
  /** Total bytes acknowledged */
  totalBytesAcknowledged: number;
  /** Current queue position (if queued) */
  queuePosition: number | null;
  /** Estimated wait time (if queued) */
  estimatedWaitTime: number | null;
  /** Generated task set ID */
  taskSetId: string | null;
  /** Last error */
  lastError: ErrorEventPayload | null;
}

// Socket.IO Client Interface
export interface NewSocketClient {
  /** Connect to the server using HTTP auth then WebSocket */
  connect(): Promise<void>;
  /** Disconnect from the server */
  disconnect(): void;
  /** Send stream_starting event - simplified flow - returns session_id from backend */
  sendStreamStarting(): Promise<string>;
  /** Send binary audio data */
  sendBinaryData(payload: BinaryDataEventPayload): Promise<void>;
  /** Send stream_completed event */
  sendStreamCompleted(): Promise<void>;
  /** Send stream_stop event */
  sendStreamStop(): Promise<void>;

  // Legacy methods (deprecated)
  /** @deprecated Use sendStreamStarting instead */
  startStreaming(payload: StartStreamingEventPayload): Promise<string>;
  /** @deprecated Use sendStreamStarting instead */
  sendStreamStarted(sessionId: string): Promise<void>;
  /** @deprecated Use sendStreamCompleted instead */
  sendStreamCancelled(sessionId: string, reason?: string): Promise<void>;
  /** Send status update (legacy) */
  sendStatusUpdate(payload: StatusUpdateEventPayload): Promise<void>;
  /** Send recording completed event (legacy) */
  sendRecordingCompleted(payload: RecordingCompletedEventPayload): Promise<void>;
  /** Send recording cancelled event (legacy) */
  sendRecordingCancelled(payload: StatusUpdateEventPayload): Promise<void>;
  /** Get current connection state */
  getConnectionState(): SocketConnectionState;
  /** Check if connected */
  isConnected(): boolean;
  /** Get session ID */
  getSessionId(): string | null;
  /** Register event listeners */
  on(event: string, handler: (data: any) => void): void;
  /** Remove event listeners */
  off(event: string, handler?: (data: any) => void): void;
}

// HTTP Authentication Service Interface
export interface SocketAuthService {
  /** Authenticate and get session credentials */
  authenticate(token: string, metadata?: Record<string, any>): Promise<SocketAuthResponse>;
  /** Check if authentication is valid */
  isAuthenticated(): boolean;
  /** Get current session token */
  getSessionToken(): string | null;
  /** Get current session ID */
  getSessionId(): string | null;
  /** Get WebSocket URL */
  getWebSocketUrl(): string | null;
  /** Get configuration */
  getConfiguration(): any;
  /** Get status */
  getStatus(): string | null;
  /** Get instructions */
  getInstructions(): any;
}
