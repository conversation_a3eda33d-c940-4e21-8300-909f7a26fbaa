export interface UserData {
  id: string;
  username: string;
  full_name?: string;
  email?: string;
  email_verified?: boolean;
  phone?: string;
  phone_verified?: boolean;
  profile_picture?: string;
  created_at: string;
  updated_at: string;
}

export interface AuthContextType {
  user: UserData | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
} 