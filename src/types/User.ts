
export interface UserData {
  id: string;
  username: string;
  email: string;
  role: string;
  token: string;
  tenantSlug?: string;
  tenantId?: string;
  tenantLabel?: string;
  tokenType?: string;
  virtueProjectNameId?: string; // Added for future use
  full_name?: string; // Added for signup
}

export interface LoginResponse {
  success: boolean;
  user?: UserData;
  message?: string;
}

export interface SignupResponse {
  success: boolean;
  message?: string;
  user_id?: string;
  username?: string;
  email?: string;
  user?: UserData; // Add user data for auto-login after signup
}

export interface SignupData {
  username: string;
  email: string;
  password: string;
  full_name?: string;
  client_id: string;
}

export interface OnboardingData {
  age: number;
  difficulty_level: number; // 1 = easy, 2 = medium, 3 = hard
  preferred_topics?: string[];
}

export interface OnboardingResponse {
  success: boolean;
  message?: string;
}
