/**
 * Route configuration for the application
 * This file defines all routes and their metadata
 */

// Route interface
export interface Route {
  path: string;
  title: string;
  description?: string;
  icon?: string;
  requiresAuth?: boolean;
  roles?: string[];
  children?: Route[];
}

// Main routes
export const routes: Route[] = [
  {
    path: '/',
    title: 'Home',
    description: 'Welcome to Audio Quiz Genie Kids',
    requiresAuth: false,
  },
  {
    path: '/onboarding',
    title: 'Setup Profile',
    description: 'Set up your learning profile',
    requiresAuth: true,
  },
  {
    path: '/dashboard',
    title: 'Dashboard',
    description: 'Your learning dashboard',
    icon: 'Home',
    requiresAuth: true,
  },
  {
    path: '/begin-learning',
    title: 'Begin Learning',
    description: 'Start your learning journey',
    icon: 'Mic',
    requiresAuth: true,
  },
  {
    path: '/tasks',
    title: 'Tasks',
    description: 'View and manage your tasks',
    icon: 'LayoutList',
    requiresAuth: true,
    children: [
      {
        path: '/tasks/:taskSetId',
        title: 'Task View',
        description: 'View task details',
        requiresAuth: true,
      },
    ],
  },
  {
    path: '/task-sets/:taskSetId',
    title: 'Task Set Details',
    description: 'View task set details',
    requiresAuth: true,
  },
  {
    path: '/anthology',
    title: 'Anthology',
    description: 'Curated learning content collection',
    icon: 'BookOpen',
    requiresAuth: true,
  },
  {
    path: '/atelier',
    title: 'Atelier',
    description: 'Discover and explore learning themes',
    icon: 'Palette',
    requiresAuth: true,
  },
  {
    path: '/playground',
    title: 'Playground',
    description: 'Content generation workspace',
    icon: 'Zap',
    requiresAuth: true,
    roles: ['admin', 'agent'],
  },
  {
    path: '*',
    title: 'Not Found',
    description: 'Page not found',
    requiresAuth: false,
  },
];

// Get route by path
export const getRouteByPath = (path: string): Route | undefined => {
  // Check main routes
  const mainRoute = routes.find(route => route.path === path);
  if (mainRoute) return mainRoute;

  // Check child routes
  for (const route of routes) {
    if (route.children) {
      const childRoute = route.children.find(child => child.path === path);
      if (childRoute) return childRoute;
    }
  }

  return undefined;
};

// Get breadcrumbs for a path
export const getBreadcrumbs = (path: string): { title: string; path: string }[] => {
  const breadcrumbs: { title: string; path: string }[] = [];

  // Don't add home breadcrumb if we're already on the dashboard
  if (path !== '/dashboard') {
    breadcrumbs.push({ title: 'Home', path: '/dashboard' });
  }

  // Split path
  const pathParts = path.split('/').filter(Boolean);

  // Build breadcrumbs
  let currentPath = '';
  for (const part of pathParts) {
    currentPath += `/${part}`;

    // Check if this is a route
    const route = getRouteByPath(currentPath);
    if (route) {
      breadcrumbs.push({ title: route.title, path: currentPath });
      continue;
    }

    // Check if this is a dynamic route
    const dynamicRoutes = routes.filter(route => route.path.includes(':'));
    for (const route of dynamicRoutes) {
      const routeParts = route.path.split('/').filter(Boolean);
      const currentParts = currentPath.split('/').filter(Boolean);

      if (routeParts.length === currentParts.length) {
        let isMatch = true;

        for (let i = 0; i < routeParts.length; i++) {
          if (routeParts[i].startsWith(':')) continue;
          if (routeParts[i] !== currentParts[i]) {
            isMatch = false;
            break;
          }
        }

        if (isMatch) {
          breadcrumbs.push({ title: route.title, path: currentPath });
          break;
        }
      }
    }
  }

  return breadcrumbs;
};
