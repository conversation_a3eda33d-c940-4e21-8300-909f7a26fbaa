
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 91% 42%;
    --primary-foreground: 210 40% 98%;

    --secondary: 25 95% 53%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 91% 42%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 0 91% 42%;
    --primary-foreground: 210 40% 98%;

    --secondary: 25 95% 53%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 0 91% 42%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }
}

.login-background {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23dc2626' fill-opacity='0.05'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20l2.83-2.83 1.41 1.41L1.41 21.41 0 22.83V20zM0 1.41l2.83 2.83L0 7.07V1.41zM20 0l2.83 2.83-1.41 1.41L18.59 1.41 20 0zM38.59 0l-2.83 2.83 1.41 1.41L40 1.41V0h-1.41zM20 18.59l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59zM30.24 11.76L35.07 6.93 37.9 9.76 33.07 14.59l-1.41-1.41zM20 38.59l-2.83-2.83 1.41-1.41L20 35.17l2.83-2.83 1.41 1.41L21.41 37.17 20 38.59z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.nepali-gradient {
  background: linear-gradient(135deg, #dc2626 0%, #ea580c 100%);
}

/* Dark mode enhancements for better visibility */
.dark .lucide-icon {
  color: #e5e7eb; /* text-gray-200 equivalent */
}

.dark button:not([disabled]) .lucide-icon {
  color: #e5e7eb; /* text-gray-200 equivalent */
}

.dark [data-state="open"] .lucide-icon {
  color: #e5e7eb; /* text-gray-200 equivalent */
}

.dark select,
.dark input,
.dark button:not([disabled]) {
  color: #e5e7eb; /* text-gray-200 equivalent */
}

.dark .text-gray-400 {
  color: #d1d5db !important; /* text-gray-300 equivalent for better contrast */
}

/* Fix for scrollable content to prevent bleed-through */
.overflow-y-auto {
  isolation: isolate;
  position: relative;
  z-index: 1;
}

/* Ensure no black text in dark mode */
.dark * {
  color-scheme: dark;
}

.dark .text-black {
  color: #e5e7eb !important; /* text-gray-200 equivalent */
}

/* Ensure no white text in light mode */
:not(.dark) .text-white {
  color: #374151 !important; /* text-gray-700 equivalent */
}

/* Fix for popover content in dark mode */
.dark [data-radix-popper-content-wrapper] * {
  color-scheme: dark;
}

.dark [data-radix-popper-content-wrapper] {
  color: #e5e7eb !important; /* text-gray-200 equivalent */
  background-color: #1f2937 !important; /* bg-gray-800 equivalent */
  border-color: #374151 !important; /* border-gray-700 equivalent */
}

/* Calendar day hover and selection styles */
.dark .rdp-day:hover:not(.rdp-day_selected) {
  background-color: #374151 !important; /* bg-gray-700 equivalent */
  color: #f9fafb !important; /* text-gray-50 equivalent */
}

.rdp-day_selected,
.rdp-day_selected:hover {
  background-color: #dc2626 !important; /* bg-primary equivalent */
  color: white !important;
}

.dark .rdp-day_selected,
.dark .rdp-day_selected:hover {
  background-color: #dc2626 !important; /* bg-primary equivalent */
  color: #f9fafb !important; /* text-gray-50 equivalent */
}

/* Calendar navigation buttons */
.dark .rdp-button:hover:not([disabled]) {
  background-color: #374151 !important; /* bg-gray-700 equivalent */
  color: #f9fafb !important; /* text-gray-50 equivalent */
}
