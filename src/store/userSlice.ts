import { createSlice, PayloadAction } from '@reduxjs/toolkit';

/**
 * Interface for user data
 */
export interface UserData {
  id: string;
  access_token: string;
  token_type: string;
  username: string;
  email: string;
  role: string;
  tenant_id: string;
  tenant_label: string;
  tenant_slug: string;
  full_name: string;
  profile_picture: string;
  auth_provider: string;
  last_login: string;
  phone_number?: string; // Added phone_number as optional
  phone_country_code?: string; // Added phone_country_code as optional
}

/**
 * Interface for user state
 */
export interface UserState {
  user: UserData | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

/**
 * Initial state for user slice
 */
const initialState: UserState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null
};

/**
 * User slice for Redux store
 */
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    /**
     * Set loading state
     */
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    /**
     * Set user data
     */
    setUser: (state, action: PayloadAction<UserData>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    },
    
    /**
     * Set error state
     */
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
      state.loading = false;
    },
    
    /**
     * Clear user data (logout)
     */
    clearUser: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
    },
    
    /**
     * Update user data
     */
    updateUser: (state, action: PayloadAction<Partial<UserData>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    }
  }
});

// Export actions
export const { setLoading, setUser, setError, clearUser, updateUser } = userSlice.actions;

// Export reducer
export default userSlice.reducer;
