import { taskService } from '../taskService';
import { httpClient } from '../httpBase';

// Mock httpClient
jest.mock('../httpBase', () => ({
  httpClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

describe('taskService', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test fetchTaskSet method
  test('fetchTaskSet should call httpClient.post with correct parameters', async () => {
    // Mock httpClient.post to return a successful response
    const mockResponse = { id: '123', tasks: [] };
    (httpClient.post as jest.Mock).mockResolvedValueOnce(mockResponse);

    // Call the fetchTaskSet method
    const result = await taskService.fetchTaskSet('123', { include_tasks: true });

    // Check if httpClient.post was called with the correct parameters
    expect(httpClient.post).toHaveBeenCalledWith(
      '/management/task-sets',
      {
        set_id: '123',
        include_tasks: true,
        include_task_ids: true,
      },
      undefined,
      undefined
    );

    // Check if the result is correct
    expect(result).toEqual(mockResponse);
  });

  // Test fetchTask method
  test('fetchTask should call httpClient.post with correct parameters', async () => {
    // Mock httpClient.post to return a successful response
    const mockResponse = { id: '123', type: 'single_choice' };
    (httpClient.post as jest.Mock).mockResolvedValueOnce(mockResponse);

    // Call the fetchTask method
    const result = await taskService.fetchTask('123', { fields: ['id', 'type'] });

    // Check if httpClient.post was called with the correct parameters
    expect(httpClient.post).toHaveBeenCalledWith(
      '/tasks/task',
      {
        task_id: '123',
        fields: ['id', 'type'],
      },
      undefined,
      undefined
    );

    // Check if the result is correct
    expect(result).toEqual(mockResponse);
  });

  // Test submitTaskAnswers method
  test('submitTaskAnswers should call httpClient.post with correct parameters', async () => {
    // Mock httpClient.post to return a successful response
    const mockResponse = { success: true, results: [] };
    (httpClient.post as jest.Mock).mockResolvedValueOnce(mockResponse);

    // Call the submitTaskAnswers method
    const answers = [{ task_id: '123', selected_option: 'a' }];
    const result = await taskService.submitTaskAnswers('123', answers);

    // Check if httpClient.post was called with the correct parameters
    expect(httpClient.post).toHaveBeenCalledWith(
      '/management/submissions/task-set',
      {
        set_id: '123',
        answers: answers,
      },
      undefined,
      undefined
    );

    // Check if the result is correct
    expect(result).toEqual(mockResponse);
  });

  // Test submitTaskAnswer method
  test('submitTaskAnswer should call httpClient.post with correct parameters', async () => {
    // Mock httpClient.post to return a successful response
    const mockResponse = { is_correct: true };
    (httpClient.post as jest.Mock).mockResolvedValueOnce(mockResponse);

    // Call the submitTaskAnswer method
    const result = await taskService.submitTaskAnswer('123', 'test', 'single_choice');

    // Check if httpClient.post was called with the correct parameters
    expect(httpClient.post).toHaveBeenCalledWith(
      '/tasks/task/submit',
      {
        task_id: '123',
        answer: 'test',
        task_type: 'single_choice',
      },
      undefined,
      undefined
    );

    // Check if the result is correct
    expect(result).toEqual(mockResponse);
  });


});
