/**
 * Test file to verify the simplified Socket.IO implementation
 */

import { newSocketClient } from '../newSocketClient';

describe('NewSocketClient - Simplified Implementation', () => {
  beforeEach(() => {
    // Reset any existing connections
    newSocketClient.disconnect();
  });

  afterEach(() => {
    // Clean up after each test
    newSocketClient.disconnect();
  });

  describe('Connection State Management', () => {
    test('should start in DISCONNECTED state', () => {
      expect(newSocketClient.getConnectionState()).toBe('DISCONNECTED');
      expect(newSocketClient.isConnected()).toBe(false);
    });

    test('should not have duplicate event handlers', () => {
      // This test verifies that we removed the duplicate stream_starting_ack handler
      const eventHandlers = (newSocketClient as any).eventHandlers;
      
      // Register a handler
      const mockHandler = jest.fn();
      newSocketClient.on('stream_starting_ack', mockHandler);
      
      // Should only have one handler registered
      expect(eventHandlers['stream_starting_ack']).toHaveLength(1);
      expect(eventHandlers['stream_starting_ack'][0]).toBe(mockHandler);
    });
  });

  describe('Simplified Audio Chunk Flow', () => {
    test('sendBinaryData should not require complex state checks', async () => {
      // Mock socket connection
      const mockSocket = {
        connected: true,
        emit: jest.fn()
      };
      (newSocketClient as any).socket = mockSocket;

      const mockPayload = {
        session_id: 'test-session',
        chunk_id: 123,
        audio_data: new Uint8Array([1, 2, 3]),
        metadata: {
          timestamp: new Date().toISOString(),
          chunk_size: 3,
          sequence_number: 1
        }
      };

      // Should not throw error and should emit directly
      await expect(newSocketClient.sendBinaryData(mockPayload)).resolves.toBeUndefined();
      expect(mockSocket.emit).toHaveBeenCalledWith('binary_data', mockPayload);
    });
  });

  describe('Stream Starting Flow', () => {
    test('sendStreamStarting should return session_id without setting ACTIVE state', async () => {
      // Mock socket connection
      const mockSocket = {
        connected: true,
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn()
      };
      (newSocketClient as any).socket = mockSocket;

      // Mock the stream_starting_ack response
      const mockSessionId = 'test-session-123';
      mockSocket.on.mockImplementation((event, handler) => {
        if (event === 'stream_starting_ack') {
          // Simulate immediate response
          setTimeout(() => handler({ session_id: mockSessionId }), 10);
        }
      });

      const sessionId = await newSocketClient.sendStreamStarting();
      
      expect(sessionId).toBe(mockSessionId);
      expect(mockSocket.emit).toHaveBeenCalledWith('stream_starting', {});
      
      // Verify that the client doesn't set ACTIVE state internally
      // (this should be managed by the hook)
      expect(newSocketClient.getConnectionState()).not.toBe('ACTIVE');
    });
  });

  describe('Event Handler Management', () => {
    test('should properly register and remove event handlers', () => {
      const mockHandler1 = jest.fn();
      const mockHandler2 = jest.fn();

      // Register handlers
      newSocketClient.on('test_event', mockHandler1);
      newSocketClient.on('test_event', mockHandler2);

      const eventHandlers = (newSocketClient as any).eventHandlers;
      expect(eventHandlers['test_event']).toHaveLength(2);

      // Remove specific handler
      newSocketClient.off('test_event', mockHandler1);
      expect(eventHandlers['test_event']).toHaveLength(1);
      expect(eventHandlers['test_event'][0]).toBe(mockHandler2);

      // Remove all handlers for event
      newSocketClient.off('test_event');
      expect(eventHandlers['test_event']).toBeUndefined();
    });
  });
});
