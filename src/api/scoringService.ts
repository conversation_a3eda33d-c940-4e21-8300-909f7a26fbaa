import { BaseService } from "./baseService";
import { ApiCallbacks } from "./httpBase";
import { SCORING_ROUTES } from "./Routes";

/**
 * Interface for user score data
 */
export interface UserScore {
  user_id: string;
  username: string;
  total_score: number;
  total_attempts: number;
  correct_answers: number;
  accuracy: number;
  last_attempt: string;
}

/**
 * Interface for leaderboard entry
 */
export interface LeaderboardEntry {
  user_id: string;
  username: string;
  total_score: number;
  accuracy: number;
  total_attempts: number;
  rank: number;
}

/**
 * Interface for leaderboard response
 */
export interface LeaderboardResponse {
  data: LeaderboardEntry[];
  meta: {
    limit: number;
    skip: number;
    total_users: number;
    sort_by: string;
  };
}

/**
 * Scoring service for handling scoring operations
 */
class ScoringService extends BaseService {
  /**
   * Get score for a specific user
   * @param userId The ID of the user
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the user score data
   */
  async getUserScore(
    userId: string,
    callbacks?: ApiCallbacks<UserScore>
  ): Promise<UserScore> {
    return this.get<UserScore>(
      SCORING_ROUTES.USER.SCORE(userId),
      undefined,
      callbacks
    );
  }

  /**
   * Get current user's score
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the current user's score data
   */
  async getCurrentUserScore(
    callbacks?: ApiCallbacks<UserScore>
  ): Promise<UserScore> {
    return this.get<UserScore>(
      SCORING_ROUTES.USER.CURRENT,
      undefined,
      callbacks
    );
  }

  /**
   * Get leaderboard of top scoring users
   * @param limit Maximum number of records (default: 10, max: 100)
   * @param skip Number of records to skip for pagination (default: 0)
   * @param sortBy Field to sort by (default: "total_score")
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the leaderboard data
   */
  async getLeaderboard(
    limit: number = 10,
    skip: number = 0,
    sortBy: string = "total_score",
    callbacks?: ApiCallbacks<LeaderboardResponse>
  ): Promise<LeaderboardResponse> {
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      skip: skip.toString(),
      sort_by: sortBy
    });

    return this.get<LeaderboardResponse>(
      `${SCORING_ROUTES.LEADERBOARD.MAIN}?${queryParams.toString()}`,
      undefined,
      callbacks
    );
  }

  /**
   * Get daily leaderboard
   * @param limit Maximum number of records (default: 10)
   * @param skip Number of records to skip (default: 0)
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the daily leaderboard data
   */
  async getDailyLeaderboard(
    limit: number = 10,
    skip: number = 0,
    callbacks?: ApiCallbacks<LeaderboardResponse>
  ): Promise<LeaderboardResponse> {
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      skip: skip.toString()
    });

    return this.get<LeaderboardResponse>(
      `${SCORING_ROUTES.LEADERBOARD.DAILY}?${queryParams.toString()}`,
      undefined,
      callbacks
    );
  }

  /**
   * Get weekly leaderboard
   * @param limit Maximum number of records (default: 10)
   * @param skip Number of records to skip (default: 0)
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the weekly leaderboard data
   */
  async getWeeklyLeaderboard(
    limit: number = 10,
    skip: number = 0,
    callbacks?: ApiCallbacks<LeaderboardResponse>
  ): Promise<LeaderboardResponse> {
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      skip: skip.toString()
    });

    return this.get<LeaderboardResponse>(
      `${SCORING_ROUTES.LEADERBOARD.WEEKLY}?${queryParams.toString()}`,
      undefined,
      callbacks
    );
  }

  /**
   * Get monthly leaderboard
   * @param limit Maximum number of records (default: 10)
   * @param skip Number of records to skip (default: 0)
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the monthly leaderboard data
   */
  async getMonthlyLeaderboard(
    limit: number = 10,
    skip: number = 0,
    callbacks?: ApiCallbacks<LeaderboardResponse>
  ): Promise<LeaderboardResponse> {
    const queryParams = new URLSearchParams({
      limit: limit.toString(),
      skip: skip.toString()
    });

    return this.get<LeaderboardResponse>(
      `${SCORING_ROUTES.LEADERBOARD.MONTHLY}?${queryParams.toString()}`,
      undefined,
      callbacks
    );
  }
}

// Export a singleton instance
export const scoringService = new ScoringService();

// Export functions for backward compatibility
export const getUserScore = (userId: string, callbacks?: ApiCallbacks<UserScore>) =>
  scoringService.getUserScore(userId, callbacks);

export const getCurrentUserScore = (callbacks?: ApiCallbacks<UserScore>) =>
  scoringService.getCurrentUserScore(callbacks);

export const getLeaderboard = (limit: number = 10, skip: number = 0, sortBy: string = "total_score", callbacks?: ApiCallbacks<LeaderboardResponse>) =>
  scoringService.getLeaderboard(limit, skip, sortBy, callbacks);

export const getDailyLeaderboard = (limit: number = 10, skip: number = 0, callbacks?: ApiCallbacks<LeaderboardResponse>) =>
  scoringService.getDailyLeaderboard(limit, skip, callbacks);

export const getWeeklyLeaderboard = (limit: number = 10, skip: number = 0, callbacks?: ApiCallbacks<LeaderboardResponse>) =>
  scoringService.getWeeklyLeaderboard(limit, skip, callbacks);

export const getMonthlyLeaderboard = (limit: number = 10, skip: number = 0, callbacks?: ApiCallbacks<LeaderboardResponse>) =>
  scoringService.getMonthlyLeaderboard(limit, skip, callbacks);
