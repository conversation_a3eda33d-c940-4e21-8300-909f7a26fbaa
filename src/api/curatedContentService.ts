import { BaseService } from "./baseService";
import { ApiCallbacks } from "./httpBase";
import { CURATED_CONTENT_ROUTES } from "./Routes";

/**
 * Interface for theme data
 */
export interface Theme {
  _id: string;
  name: string;
  name_en: string;
  description: string;
  description_en: string;
  category: string;
  icon: string;
  color: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  statistics?: {
    total_content_sets: number;
    total_content_items: number;
    average_items_per_set: number;
  };
}

/**
 * Interface for content set data
 */
export interface ContentSet {
  _id: string;
  theme_id: string;
  title: string;
  title_en: string;
  description: string;
  description_en: string;
  difficulty_level: number;
  status: string;
  gentype: string;
  task_item_ids: string[];
  total_items: number;
  created_at: string;
  theme?: {
    id: string;
    name: string;
    name_en: string;
    icon: string;
    color: string;
    category: string;
  };
}

/**
 * Interface for API response with pagination
 */
export interface ApiResponse<T> {
  data: T[];
  meta: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
  };
}

/**
 * Interface for single item API response
 */
export interface SingleApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  meta: {
    timestamp: string | null;
    request_id: string | null;
  };
}

/**
 * Interface for themes filter parameters
 */
export interface ThemesFilter {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  is_active?: boolean;
}

/**
 * Interface for content filter parameters
 */
export interface ContentFilter {
  page?: number;
  limit?: number;
  theme_id?: string;
  difficulty_level?: number;
  status?: string;
  gentype?: string;
}

/**
 * Interface for filter options response
 */
export interface FilterOptions {
  themes: Array<{
    id: string;
    name: string;
    name_en: string;
  }>;
  status_values: string[];
  gentype_values: string[];
  difficulty_levels: number[];
}

/**
 * Interface for prompt generation request
 */
export interface GeneratePromptRequest {
  content: string;
}

/**
 * Interface for prompt generation response
 */
export interface GeneratePromptResponse {
  content: string;
  generated_by: string;
  status: string;
  message: string;
}

/**
 * Interface for prompt data
 */
export interface Prompt {
  _id: string;
  content: string;
  user_id: string;
  task_set_id: string;
  created_at: string;
  status: string;
}

/**
 * Curated content service for handling curated content operations
 */
class CuratedContentService extends BaseService {
  /**
   * Fetch all themes with optional filtering
   * @param filter Optional filter parameters
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with themes response
   */
  async getThemes(
    filter?: ThemesFilter,
    callbacks?: ApiCallbacks<ApiResponse<Theme>>
  ): Promise<ApiResponse<Theme>> {
    const queryParams = new URLSearchParams();
    
    if (filter) {
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const url = `${CURATED_CONTENT_ROUTES.THEMES}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    
    return this.get<ApiResponse<Theme>>(url, undefined, callbacks);
  }

  /**
   * Fetch a single theme by ID
   * @param themeId The ID of the theme
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with theme data
   */
  async getThemeById(
    themeId: string,
    callbacks?: ApiCallbacks<SingleApiResponse<Theme>>
  ): Promise<SingleApiResponse<Theme>> {
    return this.get<SingleApiResponse<Theme>>(
      CURATED_CONTENT_ROUTES.THEME_BY_ID(themeId),
      undefined,
      callbacks
    );
  }

  /**
   * Fetch content sets for a specific theme
   * @param themeId The ID of the theme
   * @param filter Optional filter parameters
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with content sets response
   */
  async getThemeContent(
    themeId: string,
    filter?: ContentFilter,
    callbacks?: ApiCallbacks<ApiResponse<ContentSet>>
  ): Promise<ApiResponse<ContentSet>> {
    const queryParams = new URLSearchParams();
    
    if (filter) {
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });
    }

    const url = `${CURATED_CONTENT_ROUTES.THEMES_WITH_CONTENT(themeId)}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    
    return this.get<ApiResponse<ContentSet>>(url, undefined, callbacks);
  }

  /**
   * Fetch filtered content sets
   * @param filter Filter parameters
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with filtered content sets response
   */
  async getFilteredContent(
    filter: ContentFilter,
    callbacks?: ApiCallbacks<ApiResponse<ContentSet>>
  ): Promise<ApiResponse<ContentSet>> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filter).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const url = `${CURATED_CONTENT_ROUTES.FILTERED}?${queryParams.toString()}`;
    
    return this.get<ApiResponse<ContentSet>>(url, undefined, callbacks);
  }

  /**
   * Generate content from prompt
   * @param promptData The prompt data
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with generation response
   */
  async generateContent(
    promptData: GeneratePromptRequest,
    callbacks?: ApiCallbacks<GeneratePromptResponse>
  ): Promise<GeneratePromptResponse> {
    return this.post<GeneratePromptResponse>(
      CURATED_CONTENT_ROUTES.GENERATE,
      promptData,
      undefined,
      callbacks
    );
  }

  /**
   * Get all prompts
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with prompts response
   */
  async getPrompts(
    callbacks?: ApiCallbacks<ApiResponse<Prompt>>
  ): Promise<ApiResponse<Prompt>> {
    return this.post<ApiResponse<Prompt>>(
      CURATED_CONTENT_ROUTES.GET_PROMPTS,
      {},
      undefined,
      callbacks
    );
  }

  /**
   * Get filter options
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with filter options
   */
  async getFilterOptions(
    callbacks?: ApiCallbacks<SingleApiResponse<FilterOptions>>
  ): Promise<SingleApiResponse<FilterOptions>> {
    return this.get<SingleApiResponse<FilterOptions>>(
      CURATED_CONTENT_ROUTES.FILTER_OPTIONS,
      undefined,
      callbacks
    );
  }
}

// Export a singleton instance
export const curatedContentService = new CuratedContentService();

// Export functions for convenience
export const getThemes = (filter?: ThemesFilter, callbacks?: ApiCallbacks<ApiResponse<Theme>>) =>
  curatedContentService.getThemes(filter, callbacks);

export const getThemeById = (themeId: string, callbacks?: ApiCallbacks<SingleApiResponse<Theme>>) =>
  curatedContentService.getThemeById(themeId, callbacks);

export const getThemeContent = (themeId: string, filter?: ContentFilter, callbacks?: ApiCallbacks<ApiResponse<ContentSet>>) =>
  curatedContentService.getThemeContent(themeId, filter, callbacks);

export const getFilteredContent = (filter: ContentFilter, callbacks?: ApiCallbacks<ApiResponse<ContentSet>>) =>
  curatedContentService.getFilteredContent(filter, callbacks);

export const generateContent = (promptData: GeneratePromptRequest, callbacks?: ApiCallbacks<GeneratePromptResponse>) =>
  curatedContentService.generateContent(promptData, callbacks);

export const getPrompts = (callbacks?: ApiCallbacks<ApiResponse<Prompt>>) =>
  curatedContentService.getPrompts(callbacks);

export const getFilterOptions = (callbacks?: ApiCallbacks<SingleApiResponse<FilterOptions>>) =>
  curatedContentService.getFilterOptions(callbacks);
