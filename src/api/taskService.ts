import { BaseService } from "./baseService";
import { ApiCallbacks } from "./httpBase";
import { TASK_ROUTES } from "./Routes";

/**
 * Interface for file upload response
 */
export interface FileUploadResponse {
  object_name: string;
  folder: string;
  url?: string;
  content_type?: string;
  size_bytes?: number;
}

/**
 * Interface for question object structure
 */
export interface QuestionObject {
  text: string;
  translated_text: string;
  options: {
    a: string;
    b: string;
    c: string;
    [key: string]: string; // Allow for additional options like d, e, etc.
  };
  image_url?: string|null;
  answer_hint: string;
  media_url: string | null;
}

/**
 * Interface for a task item
 */
export interface Task {
  id?: string;
  _id?: string; // MongoDB ID format from backend
  type: string;
  question?: string | QuestionObject; // Support both old string format and new object format
  options?: string[]; // Keep for backward compatibility
  answer?: string;
  word?: string;
  audio_hint_url?: string;
  media_url?: string; // Unified field for images, audio, video
  status?: string;
  correct_answer?: {
    value: string;
    type: string;
  };
  user_answer?: string | string[]; // Can be single key or array of keys
  result?: string; // 'correct' or 'incorrect'
  total_score?: number;
  scored?: number;
  is_attempted?: boolean;
  submitted?: boolean;
  submitted_at?: string;
  [key: string]: any; // Allow for additional properties
}

/**
 * Interface for a task set
 */
export interface TaskSet {
  id: string;
  tasks?: Task[];
  task_ids?: string[];
  created_at: string;
  user_id?: string;
  input_type?: string;
  input_content?: string;
  status?: string;
  total_score?: number;
  input_metadata?: {
    object_name: string;
    folder: string;
    bucket?: string;
    content_type?: string;
    size_bytes?: number;
  };
  [key: string]: any; // Allow for additional properties
}

/**
 * Interface for task answer submission (for task sets)
 */
export interface TaskAnswer {
  task_id: string;
  selected_option: string; // For single/multiple choice tasks - the option key (a, b, c, etc.)
}

/**
 * Interface for individual task item submission
 */
export interface TaskItemSubmission {
  task_id: string;
  answer: Answer;
  task_type: QuizType;
  folder?: string; // Optional folder for file storage
}

/**
 * Answer types for different task types
 * Can be a string (like "a") or array of strings (like ["a", "b"])
 */
export type Answer = string | string[];

/**
 * Quiz/Task types
 */
export type QuizType = 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | 'speak_word' | 'audio_task' | string;

/**
 * Interface for task submission result
 */
export interface TaskSubmissionResult {
  success: boolean;
  results: {
    task_id: string;
    is_correct: boolean;
    score: number;
    correct_answer?: string;
    feedback?: string;
  }[];
  total_score: number;
  user_score?: {
    total_score: number;
    total_attempts: number;
    correct_answers: number;
    accuracy: number;
  };
}

/**
 * Interface for task set request options
 */
export interface TaskSetRequestOptions {
  include_tasks?: boolean;
  include_task_ids?: boolean;
  fields?: string[];
}

/**
 * Interface for task request options
 */
export interface TaskRequestOptions {
  fields?: string[];
}

/**
 * Interface for pagination metadata
 */
export interface PaginationMeta {
  limit: number;
  skip: number;
  total: number;
  has_more: boolean;
  count: number;
}

/**
 * Interface for paginated task sets response
 */
export interface PaginatedTaskSets {
  data: TaskSet[];
  meta: PaginationMeta;
}



/**
 * Interface for score response (both task item and task set)
 * Task item format: {score: number, max_score: number}
 * Task set format: {score: number, total_score: number, percentage: number, total_tasks: number, attempted_tasks: number, status: string}
 */
export interface ScoreResponse {
  score: number;
  max_score?: number; // For task items
  total_score?: number; // For task sets
  percentage?: number; // For task sets
  total_tasks?: number; // For task sets
  attempted_tasks?: number; // For task sets
  status?: string; // For task sets
}

/**
 * Task service for handling task operations
 */
class TaskService extends BaseService {
  /**
   * Fetch a task set by ID with options for field filtering
   * @param taskSetId The ID of the task set to fetch
   * @param options Optional request options for filtering fields
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the task set data
   */
  async fetchTaskSet(
    taskSetId: string,
    options?: TaskSetRequestOptions,
    callbacks?: ApiCallbacks<TaskSet>
  ): Promise<TaskSet> {
    const queryParams = new URLSearchParams();

    if (options?.include_tasks) {
      queryParams.append('include_tasks', 'true');
    }
    if (options?.fields && options.fields.length > 0) {
      options.fields.forEach(field => queryParams.append('fields', field));
    }

    const url = `${TASK_ROUTES.TASK_SET(taskSetId)}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    return this.get<TaskSet>(url, undefined, callbacks);
  }

  /**
   * Fetch a single task by ID with options for field filtering
   * @param taskId The ID of the task to fetch
   * @param options Optional request options for filtering fields
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the task data
   */
  async fetchTask(
    taskId: string,
    options?: TaskRequestOptions,
    callbacks?: ApiCallbacks<Task>
  ): Promise<Task> {
    const queryParams = new URLSearchParams();

    if (options?.fields && options.fields.length > 0) {
      options.fields.forEach((field: string) => queryParams.append('fields', field));
    }

    const url = `${TASK_ROUTES.TASK(taskId)}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    return this.get<Task>(url, undefined, callbacks);
  }

  /**
   * Submit answers for a task set
   * @param taskSetId The ID of the task set
   * @param answers Array of task answers
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the submission result
   */
  async submitTaskAnswers(
    taskSetId: string,
    answers: TaskAnswer[],
    callbacks?: ApiCallbacks<TaskSubmissionResult>
  ): Promise<TaskSubmissionResult> {
    return this.post<TaskSubmissionResult>(
      TASK_ROUTES.TASK_SET_SUBMIT,
      {
        set_id: taskSetId,
        answers: answers
      },
      undefined,
      callbacks
    );
  }

  /**
   * Submit an answer for a single task
   * @param taskId The ID of the task
   * @param answer The answer - string for single choice (like "a"), array for multiple choice (like ["a", "b"])
   * @param taskType The type of the task
   * @param folder Optional folder for file storage (only for audio tasks, otherwise null)
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the submission result for the single task
   */
  async submitTaskAnswer(
    taskId: string,
    answer: Answer,
    taskType: QuizType,
    folder?: string | null,
    callbacks?: ApiCallbacks<any>
  ): Promise<any> {
    const requestBody: TaskItemSubmission = {
      task_id: taskId,
      answer: answer, // Direct assignment - string or string[]
      task_type: taskType,
      folder: (taskType === 'speak_word' || taskType === 'audio_task') ? (folder || 'recordings') : null
    };

    return this.post<any>(
      TASK_ROUTES.TASK_SUBMIT,
      requestBody,
      undefined,
      callbacks
    );
  }

  /**
   * Fetch user's task sets with filtering and pagination
   * @param page Page number (default: 1)
   * @param limit Maximum number of records to return (default: 10)
   * @param sortBy Field to sort by (default: "created_at")
   * @param sortOrder Sort order (1 for ascending, -1 for descending, default: -1)
   * @param status Optional status filter
   * @param search Optional search query
   * @param fields Optional fields to retrieve
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with paginated list of task sets
   */
  async fetchUserTaskSets(
    page: number = 1,
    limit: number = 10,
    sortBy: string = "created_at",
    sortOrder: number = -1,
    status?: string,
    search?: string,
    fields?: string[],
    callbacks?: ApiCallbacks<PaginatedTaskSets>
  ): Promise<PaginatedTaskSets> {
    // Build query parameters
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      sort_by: sortBy,
      sort_order: sortOrder.toString()
    });

    // Add optional parameters
    if (status) params.append('status', status);
    if (search) params.append('search', search);
    if (fields && fields.length > 0) {
      fields.forEach((field: string) => params.append('fields', field));
    }

    return this.get<PaginatedTaskSets>(
      `${TASK_ROUTES.USER_TASK_SETS}?${params.toString()}`,
      undefined,
      callbacks
    );
  }



  /**
   * Fetch score for a specific task set
   * @param taskSetId The ID of the task set
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the task set score data
   */
  async fetchTaskSetScore(
    taskSetId: string,
    callbacks?: ApiCallbacks<ScoreResponse>
  ): Promise<ScoreResponse> {
    return this.get<ScoreResponse>(
      TASK_ROUTES.TASK_SET_SCORE(taskSetId),
      undefined,
      callbacks
    );
  }

  /**
   * Fetch score for a specific task item
   * @param taskItemId The ID of the task item
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with the task item score data
   */
  async fetchTaskItemScore(
    taskItemId: string,
    callbacks?: ApiCallbacks<ScoreResponse>
  ): Promise<ScoreResponse> {
    return this.get<ScoreResponse>(
      TASK_ROUTES.TASK_ITEM_SCORE(taskItemId),
      undefined,
      callbacks
    );
  }

  /**
   * Fetch all tasks for a specific task set
   * @param setId The ID of the task set
   * @param fields Optional fields to retrieve
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with array of tasks
   */
  async fetchTaskSetTasks(
    setId: string,
    fields?: string[],
    callbacks?: ApiCallbacks<Task[]>
  ): Promise<Task[]> {
    const queryParams = new URLSearchParams();

    if (fields && fields.length > 0) {
      fields.forEach((field: string) => queryParams.append('fields', field));
    }

    const url = `${TASK_ROUTES.TASK_SET_TASKS(setId)}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

    return this.get<Task[]>(url, undefined, callbacks);
  }
}

// Export a singleton instance
export const taskService = new TaskService();

// Export functions for backward compatibility
export const fetchTaskSet = (taskSetId: string, _user: any, options?: TaskSetRequestOptions) =>
  taskService.fetchTaskSet(taskSetId, options);

export const fetchTask = (taskId: string, _user: any, options?: TaskRequestOptions) =>
  taskService.fetchTask(taskId, options);

export const submitTaskAnswers = (taskSetId: string, answers: TaskAnswer[], _user: any) =>
  taskService.submitTaskAnswers(taskSetId, answers);

export const submitTaskAnswer = (taskId: string, answer: Answer, _user: any, taskType: QuizType, folder?: string | null) =>
  taskService.submitTaskAnswer(taskId, answer, taskType, folder);

export const fetchUserTaskSets = (_user: any, page: number = 1, limit: number = 10, sortBy: string = "created_at", sortOrder: number = -1, status?: string, search?: string, fields?: string[]) =>
  taskService.fetchUserTaskSets(page, limit, sortBy, sortOrder, status, search, fields);



export const fetchTaskSetScore = (taskSetId: string, _user: any) =>
  taskService.fetchTaskSetScore(taskSetId);

export const fetchTaskItemScore = (taskItemId: string, _user: any) =>
  taskService.fetchTaskItemScore(taskItemId);

export const fetchTaskSetTasks = (setId: string, _user: any, fields?: string[]) =>
  taskService.fetchTaskSetTasks(setId, fields);

// fetch all taks of a user
