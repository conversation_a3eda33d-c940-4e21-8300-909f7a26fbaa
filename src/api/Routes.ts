/**
 * Centralized API routes for the Learn Fluent Nepali application
 * This file provides a single source of truth for all API routes
 * making it easier to update route names in the future.
 */

// Base API URL from environment variables
let apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000/v1';

// Ensure the URL doesn't have a trailing slash
export const BASE_API_URL = apiUrl.replace(/\/$/, '');

// Auth Service Routes
export const AUTH_ROUTES = {
  VALIDATE_TENANT: '/auth/get_tenant_id',
  LOGIN: '/auth/login',
  SIGNUP: '/auth/signup',
  ONBOARDING: '/auth/onboarding',
  GOOGLE_AUTH: '/auth/google-auth',
  HEALTH: '/auth/health',
};

// Task Service Routes (Legacy - most moved to Management Service)
export const TASK_ROUTES = {
  GENERATE: '/tasks/generate',
  SAMPLE: '/tasks/sample',
  TASK_SET: (taskSetId: string) => `/management/task-sets/${taskSetId}`,
  TASK: (taskId: string) => `/management/task-items/${taskId}`,
  TASK_SET_SUBMIT: '/management/submissions/task-set',
  TASK_SUBMIT: '/management/submissions/task-item',
  USER_TASK_SETS: '/management/task-sets/filtered',
  TASK_SET_SCORE: (taskSetId: string) => `/management/task-sets/score/${taskSetId}`,
  TASK_ITEM_SCORE: (taskItemId: string) => `/management/task-items/score/${taskItemId}`,
  TASK_SET_TASKS: (setId: string) => `/management/task-items/set/${setId}/tasks`,
  TEST_SCORE: '/tasks/test_score', // Legacy test score endpoint
  WEBSOCKET: {
    STREAM: (streamType: string) => `/tasks/ws/stream/${streamType}`,
  },
  HEALTH: '/tasks/health',
};

// Task Management Service Routes
export const TASK_MANAGEMENT_ROUTES = {
  TASK_SETS: '/management/task-sets',
  TASK_SETS_FILTERED: '/management/task-sets/filtered',
  TASK_SET_BY_ID: (taskSetId: string) => `/management/task-sets/${taskSetId}`,
  SUBMISSIONS: {
    CREATE: '/management/submissions',
    USER: (userId: string) => `/management/submissions/user/${userId}`,
    TASK_SET: (taskSetId: string) => `/management/submissions/task-set/${taskSetId}`,
    BY_ID: (submissionId: string) => `/management/submissions/${submissionId}`,
  },
  SCORING: {
    SCORE: '/management/scoring/score',
    TASK_SET: (taskSetId: string) => `/management/scoring/task-set/${taskSetId}`,
    USER: (userId: string) => `/management/scoring/user/${userId}`,
  },
  HEALTH: '/management/health',
};

// Media Service Routes (now part of Management Service)
export const MEDIA_ROUTES = {
  FILE: '/management/media/file',
  UPLOAD: '/management/media/upload',
  HEALTH: '/management/health',
};

// Scoring Service Routes (now part of Management Service)
export const SCORING_ROUTES = {
  LEADERBOARD: {
    MAIN: '/management/scoring/leaderboard',
    DAILY: '/management/scoring/leaderboard/daily',
    WEEKLY: '/management/scoring/leaderboard/weekly',
    MONTHLY: '/management/scoring/leaderboard/monthly',
  },
  USER: {
    SCORE: (userId: string) => `/management/scoring/user/${userId}`,
    CURRENT: '/management/scoring/user/current',
    HISTORY: (userId: string) => `/management/scoring/users/${userId}/history`,
    BADGES: (userId: string) => `/management/scoring/users/${userId}/badges`,
  },
  TASK_SET: {
    SCORES: (taskSetId: string) => `/management/scoring/task-sets/${taskSetId}/scores`,
    LEADERBOARD: (taskSetId: string) => `/management/scoring/task-sets/${taskSetId}/leaderboard`,
  },
  HEALTH: '/management/health',
};

// API Gateway Routes
export const GATEWAY_ROUTES = {
  CONFIG: '/config',
  HEALTH: '/health',
  SERVICES_STATUS: '/services/status',
};

// Socket Service Routes
export const SOCKET_ROUTES = {
  CONNECT: '/socket/connect',
  VALIDATE: (sessionToken: string) => `/socket/validate/${sessionToken}`,
  SESSION_CLEANUP: (sessionToken: string) => `/socket/session/${sessionToken}`,
  SESSION_STATUS: (sessionToken: string) => `/socket/session/${sessionToken}/status`,
  STATUS: '/socket/status',
  HEALTH: '/socket/health',
  HEALTH_REDIS: '/socket/health/redis',
};

// WebSocket Routes (Legacy)
export const WEBSOCKET_ROUTES = {
  CONNECT: '/v1/ws/connect',
  LEGACY_STREAM: (streamType: string) => `/v1/ws/stream/${streamType}`
};

// Socket.IO Routes
export const SOCKETIO_ROUTES = {
  CONNECT: '/socket/socket.io', // Updated to use Socket Service path
  AUDIO: 'audio',
  NAMESPACE: '' // Default namespace
};

// Curated Content Management Routes
export const CURATED_CONTENT_ROUTES = {
  THEMES: '/management/curated/themes',
  THEME_BY_ID: (themeId: string) => `/management/curated/theme/${themeId}`,
  THEMES_WITH_CONTENT: (themeId: string) => `/management/curated/themes/${themeId}`,
  FILTERED: '/management/curated/filtered',
  GENERATE: '/management/curated/generate',
  GET_PROMPTS: '/management/curated/get_prompts',
  FILTER_OPTIONS: '/management/curated/filter-options',
};