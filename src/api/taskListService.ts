import { BaseService } from "./baseService";
import { ApiCallbacks, RequestOptions, httpClient } from "./httpBase";
import { cacheManager } from "./cacheManager";
import { TASK_MANAGEMENT_ROUTES } from "./Routes";

/**
 * Interface for task set filter parameters
 */
export interface TaskSetFilter {
  page: number;
  limit: number;
  sort_by: string;
  sort_order: number;
  status?: string;
  search?: string;
  start_date?: string;
  end_date?: string;
  user_id?: string;
  fields?: string[];
  _timestamp?: number; // Used to force refresh without changing other parameters
}

/**
 * Interface for a task set
 */
export interface TaskSet {
  id?: string;
  _id?: string; // Some APIs might return _id instead of id
  user_id: string;
  input_type: string;
  input_content: string;
  tasks: string[];
  status: string;
  created_at: string;
  completed_at?: string | null;
  max_score?: number;
  score?: number;
  scored?: number;
  remark?: string | null;
  input_metadata?: {
    object_name: string;
    folder: string;
    bucket: string;
    content_type: string;
    size_bytes: number;
  };
}

/**
 * Interface for task set response
 */
export interface TaskSetResponse {
  items: TaskSet[];
  total?: number;
  page?: number;
  limit?: number;
  pages?: number;
  pagination?: {
    page: number;
    limit: number;
    total_count: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

/**
 * Interface for the actual API response format
 */
export interface ApiTaskSetResponse {
  data: TaskSet[];
  meta: {
    page: number;
    limit: number;
    total: number;
    total_pages?: number;
    has_more?: boolean;
    count?: number;
  };
}

/**
 * Task list service for handling task set operations
 */
class TaskListService extends BaseService {
  /**
   * Fetch task sets with filtering and pagination
   * @param filter The filter parameters
   * @param callbacks Optional callbacks for success, error, and finally
   * @param options Optional request options for caching and cancellation
   * @returns Promise with the task set response
   */
  async fetchTaskSets(
    filter: TaskSetFilter,
    callbacks?: ApiCallbacks<ApiTaskSetResponse>,
    options?: RequestOptions
  ): Promise<TaskSetResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      Object.entries(filter).forEach(([key, value]) => {
        // Skip internal parameters that start with underscore
        if (key.startsWith('_')) {
          return;
        }

        if (value !== undefined && value !== null && value !== '') {
          // Ensure sort_order is passed as a number (1 or -1)
          if (key === 'sort_order') {
            queryParams.append(key, value.toString());
            console.log(`Setting sort_order to ${value} (${typeof value})`);
          } else {
            queryParams.append(key, value.toString());
          }
        }
      });

      const url = `${TASK_MANAGEMENT_ROUTES.TASK_SETS_FILTERED}?${queryParams.toString()}`;
      console.log('Fetching task sets from:', url, 'with filter:', filter);

      // Make API request with caching and cancellation options
      const apiResponse = await this.get<any>(url, options, callbacks);
      console.log('API response data:', apiResponse);

      // Handle different response formats
      if (!apiResponse) {
        console.error('API response is null or undefined');
        return {
          items: [],
          total: 0,
          page: filter.page,
          limit: filter.limit,
          pages: 0
        };
      }

      // Check if the response has items property (new format)
      if ('items' in apiResponse && Array.isArray(apiResponse.items)) {
        console.log('Response has items array property:', apiResponse.items.length, 'items');

        // If it also has pagination, use that
        if (apiResponse.pagination) {
          console.log('Response has pagination:', apiResponse.pagination);
          return {
            items: apiResponse.items,
            total: apiResponse.pagination.total_count,
            page: apiResponse.pagination.page,
            limit: apiResponse.pagination.limit,
            pages: apiResponse.pagination.total_pages,
            pagination: apiResponse.pagination
          };
        }

        // Otherwise just return the items
        return {
          items: apiResponse.items,
          total: apiResponse.items.length,
          page: filter.page,
          limit: filter.limit,
          pages: Math.ceil(apiResponse.items.length / filter.limit)
        };
      }

      // Check if the response has a data property that is an array (old format)
      if (apiResponse.data && Array.isArray(apiResponse.data)) {
        console.log('Response has data array property, transforming to expected format');

        // Get metadata from response or use defaults
        const meta = apiResponse.meta || {};

        return {
          items: apiResponse.data,
          total: meta.total || apiResponse.data.length,
          page: meta.page || filter.page,
          limit: meta.limit || filter.limit,
          pages: meta.total_pages || Math.ceil((meta.total || apiResponse.data.length) / filter.limit)
        };
      }

      // Check if the response itself is an array
      if (Array.isArray(apiResponse)) {
        console.log('API response is an array, using it directly');
        return {
          items: apiResponse,
          total: apiResponse.length,
          page: filter.page,
          limit: filter.limit,
          pages: Math.ceil(apiResponse.length / filter.limit)
        };
      }

      // If we get here, we don't know how to handle the response
      console.error('Unknown API response format:', apiResponse);
      return {
        items: [],
        total: 0,
        page: filter.page,
        limit: filter.limit,
        pages: 0
      };
    } catch (error) {
      console.error('Error in fetchTaskSets:', error);
      throw error;
    }
  }
}

/**
 * Clear cache for a specific key
 * @param cacheKey The cache key to clear
 */
class TaskListServiceWithCache extends TaskListService {
  clearCache(cacheKey?: string): void {
    if (cacheKey) {
      cacheManager.delete(cacheKey);
    } else {
      // Clear all task list related cache entries
      const allKeys = cacheManager.keys();
      allKeys.forEach(key => {
        if (key.startsWith('taskList:')) {
          cacheManager.delete(key);
        }
      });
    }
  }
}

// Export a singleton instance
export const taskListService = new TaskListServiceWithCache();

/**
 * Hook for using the task list service
 * @returns The task list service methods
 */
export const useTaskListService = () => {
  return {
    fetchTaskSets: (
      filter: TaskSetFilter,
      callbacks?: ApiCallbacks<ApiTaskSetResponse>,
      options?: RequestOptions
    ) => taskListService.fetchTaskSets(filter, callbacks, options),
    clearCache: (cacheKey?: string) => taskListService.clearCache(cacheKey)
  };
};
