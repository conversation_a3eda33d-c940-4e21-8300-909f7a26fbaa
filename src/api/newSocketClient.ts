/**
 * New Socket.IO Client Implementation
 * Following the provided API specification with proper authentication flow
 */

import { io, Socket } from 'socket.io-client';
import { socketAuthService } from './socketAuth';
import type {
  NewSocketClient,
  SocketConnectionState,
  StartStreamingEventPayload,
  BinaryDataEventPayload,
  StatusUpdateEventPayload,
  RecordingCompletedEventPayload
} from '../types/socketTypes';

/**
 * New Socket.IO Client Implementation
 */
class NewSocketClientImpl implements NewSocketClient {
  private socket: Socket | null = null;
  private connectionState: SocketConnectionState = 'DISCONNECTED';
  private eventHandlers: Record<string, Array<(data: any) => void>> = {};

  private maxReconnectAttempts = 3;
  private reconnectDelay = 1000;

  /**
   * Connect to the server using HTTP auth then WebSocket
   */
  async connect(): Promise<void> {
    try {
      console.log('🔌 Starting new Socket.IO connection process');

      // Ensure any existing socket is disconnected first
      if (this.socket) {
        console.log('🔌 Disconnecting existing socket before new connection');
        this.socket.disconnect();
        this.socket = null;
      }

      // Step 1: HTTP Authentication
      console.log('🔐 Starting HTTP authentication');
      const { getTokenFromStorage } = await import('./socketAuth');
      const token = getTokenFromStorage();
      console.log('🔍 Debug - Token retrieved:', token ? 'Present' : 'Missing');

      if (!token) {
        throw new Error('No authentication token available');
      }

      // Always re-authenticate to get fresh session credentials
      console.log('🔍 Debug - About to call socketAuthService.authenticate()');
      await socketAuthService.authenticate(token);
      console.log('✅ HTTP authentication completed');

      // Step 2: Get WebSocket connection details
      const websocketPath = socketAuthService.getWebSocketUrl();
      const sessionToken = socketAuthService.getSessionToken();
      const sessionId = socketAuthService.getSessionId();

      console.log('🔍 Debug - Authentication credentials:');
      console.log('  websocketPath:', websocketPath);
      console.log('  sessionToken:', sessionToken ? 'Present' : 'Missing');
      console.log('  sessionId:', sessionId);

      if (!websocketPath || !sessionToken || !sessionId) {
        throw new Error(`Missing authentication credentials: websocketPath=${websocketPath}, sessionToken=${sessionToken ? 'present' : 'missing'}, sessionId=${sessionId}`);
      }

      // Debug logging
      console.log('🔍 Debug - WebSocket URL construction:');
      console.log('  websocketPath from auth:', websocketPath);
      console.log('  VITE_API_URL:', import.meta.env.VITE_API_URL);

      // Construct full WebSocket URL
      // The websocketPath should be "/v1/socket/socket.io" from the API response
      const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8204/v1';
      console.log('  baseUrl:', baseUrl);

      // Remove /v1 from base URL if it exists, since websocketPath already includes it
      const cleanBaseUrl = baseUrl.replace(/\/v1$/, '');
      console.log('  cleanBaseUrl:', cleanBaseUrl);

      const fullWebSocketUrl = `${cleanBaseUrl}${websocketPath}`;
      console.log('  fullWebSocketUrl:', fullWebSocketUrl);

      console.log(`🔗 Connecting to WebSocket: ${fullWebSocketUrl}`);

      // Step 3: Establish WebSocket connection
      console.log('🔍 Debug - About to call connectWebSocket()');
      await this.connectWebSocket(fullWebSocketUrl, sessionToken);
      console.log('🔍 Debug - connectWebSocket() completed');

      console.log('✅ Socket.IO connection established successfully');
    } catch (error) {
      console.error('❌ Socket.IO connection failed:', error);
      console.error('❌ Connection error details:', error.message, error.stack);
      this.setConnectionState('ERROR');
      throw error;
    }
  }

  /**
   * Establish WebSocket connection with authentication
   * @private
   */
  private async connectWebSocket(url: string, sessionToken: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // Close existing connection if any
      if (this.socket) {
        this.socket.disconnect();
        this.socket = null;
      }

      console.log('🔍 Debug - Creating Socket.IO connection with URL:', url);

      // Validate URL before creating connection
      if (!url || url.trim() === '') {
        reject(new Error('Invalid WebSocket URL: URL is empty or undefined'));
        return;
      }

      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        reject(new Error(`Invalid WebSocket URL format: ${url}`));
        return;
      }

      // Parse URL to extract base and path for Socket.IO
      const urlObj = new URL(url);
      const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
      const path = urlObj.pathname;

      console.log('🔍 Debug - Socket.IO URL parsing:');
      console.log('  Original URL:', url);
      console.log('  Base URL:', baseUrl);
      console.log('  Path:', path);

      // Create new Socket.IO connection with explicit path
      console.log('🔍 Debug - Final Socket.IO configuration:');
      console.log('  Connecting to baseUrl:', baseUrl);
      console.log('  Using path:', path);
      console.log('  Session token present:', !!sessionToken);

      this.socket = io(baseUrl, {
        path: path, // Explicitly set the path (e.g., "/v1/socket/socket.io")
        auth: {
          session_token: sessionToken
        },
        transports: ['polling', 'websocket'], // Start with HTTP polling, then upgrade to WebSocket
        timeout: 10000, // 10 second timeout
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        forceNew: true, // Force a new connection
        autoConnect: false, // Don't auto-connect, we'll connect manually
        upgrade: true, // Allow upgrade to WebSocket after HTTP connection
        rememberUpgrade: true // Remember successful upgrades
      });

      // Manually connect after configuration
      console.log('🔗 Manually connecting Socket.IO...');
      this.socket.connect();

      // Set up connection event handlers
      this.socket.on('connect', () => {
        console.log('🔗 WebSocket connected');
        this.setConnectionState('CONNECTED');

        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ WebSocket connection error:', error);
        this.setConnectionState('ERROR');
        reject(new Error(`WebSocket connection failed: ${error.message}`));
      });

      this.socket.on('disconnect', (reason) => {
        console.log('🔌 WebSocket disconnected:', reason);
        this.setConnectionState('DISCONNECTED');
        this.triggerEvent('disconnect', { reason });
      });

      // Set up API event handlers
      this.setupEventHandlers();

      // Connection timeout
      setTimeout(() => {
        if (this.connectionState !== 'CONNECTED') {
          reject(new Error('WebSocket connection timeout'));
        }
      }, 10000);
    });
  }

  /**
   * Set up Socket.IO event handlers for the simplified API specification
   * @private
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    // Connection acknowledgment
    this.socket.on('connect_ack', (data: any) => {
      console.log('📡 Received connect_ack:', data);
      this.setConnectionState('CONNECTED');
      this.triggerEvent('connect_ack', data);
    });

    // Simplified flow events
    this.socket.on('stream_starting_ack', (data: any) => {
      console.log('📡 Received stream_starting_ack:', data);
      this.setConnectionState('ACTIVE');
      this.triggerEvent('stream_starting_ack', data);
    });

    this.socket.on('stream_completed_ack', (data: any) => {
      console.log('📡 Received stream_completed_ack:', data);
      this.setConnectionState('COMPLETED');
      this.triggerEvent('stream_completed_ack', data);
    });

    this.socket.on('stream_stop_ack', (data: any) => {
      console.log('📡 Received stream_stop_ack:', data);
      this.setConnectionState('CANCELLED');
      this.triggerEvent('stream_stop_ack', data);
    });

    this.socket.on('task_generation_processing', (data: any) => {
      console.log('📡 Received task_generation_processing:', data);
      this.triggerEvent('task_generation_processing', data);
    });

    this.socket.on('task_generation_complete', (data: any) => {
      console.log('📡 Received task_generation_complete:', data);
      this.setConnectionState('COMPLETED');
      this.triggerEvent('task_generation_complete', data);
    });

    this.socket.on('task_generation_failed', (data: any) => {
      console.error('📡 Received task_generation_failed:', data);
      this.setConnectionState('ERROR');
      this.triggerEvent('task_generation_failed', data);
    });

    this.socket.on('task_generation_cancelled', (data: any) => {
      console.log('📡 Received task_generation_cancelled:', data);
      this.triggerEvent('task_generation_cancelled', data);
    });

    this.socket.on('stream_error', (data: any) => {
      console.error('📡 Received stream_error:', data);
      this.setConnectionState('ERROR');
      this.triggerEvent('stream_error', data);
    });

    // Chunk acknowledgment
    this.socket.on('chunk_received', (data: any) => {
      console.log(`✅ Chunk received acknowledgment:`, data);
      this.triggerEvent('chunk_received', data);
    });

    // Task generated
    this.socket.on('task_generated', (data: any) => {
      console.log('🎯 Task generated:', data);
      this.triggerEvent('task_generated', data);
    });

    // Processing complete
    this.socket.on('processing_complete', (data: any) => {
      console.log('✅ Processing complete:', data);
      this.triggerEvent('processing_complete', data);
    });

    // Stream finished
    this.socket.on('stream_finished', (data: any) => {
      console.log('🏁 Stream finished:', data);
      this.setConnectionState('COMPLETED');
      this.triggerEvent('stream_finished', data);
    });

    // Stream cancelled acknowledgment
    this.socket.on('stream_cancelled_ack', (data: any) => {
      console.log('❌ Stream cancelled ack:', data);
      this.setConnectionState('CANCELLED');
      this.triggerEvent('stream_cancelled_ack', data);
    });

    // Queue status
    this.socket.on('queue_status', (data: any) => {
      console.log('⏳ Queue status:', data);
      this.triggerEvent('queue_status', data);
    });

    // Queue updates
    this.socket.on('queue_update', (data: any) => {
      console.log('📊 Queue update:', data);
      this.triggerEvent('queue_update', data);
    });

    // Error handling
    this.socket.on('error', (data: any) => {
      console.error('❌ Server error:', data);
      this.setConnectionState('ERROR');
      this.triggerEvent('error', data);
    });
  }

  /**
   * Disconnect from the server
   */
  disconnect(): void {
    console.log('🔌 Disconnecting Socket.IO client');

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.setConnectionState('DISCONNECTED');
    this.eventHandlers = {};
  }

  /**
   * Send stream_starting event - using session_id from HTTP response
   */
  async sendStreamStarting(): Promise<string> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    if (this.connectionState !== 'CONNECTED') {
      throw new Error(`Invalid state for streaming: ${this.connectionState}`);
    }

    // Get session_id from the HTTP authentication response
    const sessionId = socketAuthService.getSessionId();
    if (!sessionId) {
      throw new Error('No session_id available from HTTP authentication');
    }

    console.log('🎵 Sending stream_starting event with session_id from HTTP response:', sessionId);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        console.error('❌ Timeout waiting for stream_starting_ack - server may not be handling this event');
        console.error('❌ Make sure your server has a handler for "stream_starting" event');
        reject(new Error('Stream starting timeout'));
      }, 5000);

      // Listen for stream_starting_ack response
      const handleStreamStartingAck = (data: any) => {
        console.log('📡 Received stream_starting_ack:', data);
        clearTimeout(timeout);
        this.socket?.off('stream_starting_ack', handleStreamStartingAck);
        console.log('✅ Backend acknowledged stream_starting, using original session_id:', sessionId);
        // Return the original session_id from HTTP response, not from the ack
        resolve(sessionId);
      };

      console.log('🔍 Setting up listener for stream_starting_ack event');
      this.socket!.on('stream_starting_ack', handleStreamStartingAck);

      // Send stream_starting with session_id from HTTP response
      const payload = { session_id: sessionId };
      console.log('📤 Emitting stream_starting event with payload:', payload);
      this.socket!.emit('stream_starting', payload);

      console.log('⏳ Waiting for stream_starting_ack response from server...');
    });
  }

  /**
   * Send stream_completed event - with session_id from HTTP response
   */
  async sendStreamCompleted(): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    // Get session_id from the HTTP authentication response
    const sessionId = socketAuthService.getSessionId();
    if (!sessionId) {
      throw new Error('No session_id available from HTTP authentication');
    }

    const payload = { session_id: sessionId };
    console.log('🏁 Sending stream_completed event with payload:', payload);
    this.socket.emit('stream_completed', payload);
    this.setConnectionState('COMPLETED');
  }

  /**
   * Send stream_stop event - with session_id from HTTP response
   */
  async sendStreamStop(): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    // Get session_id from the HTTP authentication response
    const sessionId = socketAuthService.getSessionId();
    if (!sessionId) {
      throw new Error('No session_id available from HTTP authentication');
    }

    const payload = { session_id: sessionId };
    console.log('🛑 Sending stream_stop event with payload:', payload);
    this.socket.emit('stream_stop', payload);
    this.setConnectionState('CANCELLED');
  }

  /**
   * Start audio streaming (legacy)
   * @deprecated Use sendStreamStarting instead
   */
  async startStreaming(payload: StartStreamingEventPayload): Promise<string> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    if (this.connectionState !== 'CONNECTED') {
      throw new Error(`Invalid state for streaming: ${this.connectionState}`);
    }

    console.log('🎵 Starting audio streaming:', payload);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Start streaming timeout'));
      }, 5000);

      // Listen for start_streaming response (backend ready)
      const handleStartStreamingResponse = (data: any) => {
        console.log('📡 Received start_streaming response:', data);
        if (data.status === 'READY' && data.session_id) {
          clearTimeout(timeout);
          this.socket?.off('start_streaming', handleStartStreamingResponse);
          this.setConnectionState('STARTED');
          console.log('✅ Backend ready for streaming, session_id:', data.session_id);
          resolve(data.session_id); // Return the session_id from backend
        }
      };

      this.socket!.on('start_streaming', handleStartStreamingResponse);
      this.socket!.emit('start_streaming', payload);
    });
  }

  /**
   * Send binary audio data - simplified
   */
  async sendBinaryData(payload: BinaryDataEventPayload): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    console.log(`📤 Sending binary chunk #${payload.chunk_id}: ${payload.audio_data.length} bytes`);
    this.socket.emit('binary_data', payload);
  }

  /**
   * Send stream started confirmation (frontend confirmation)
   */
  async sendStreamStarted(sessionId: string): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    if (this.connectionState !== 'STARTED') {
      throw new Error(`Invalid state for stream confirmation: ${this.connectionState}`);
    }

    const payload = {
      session_id: sessionId
    };

    console.log('📡 Sending stream_started confirmation:', payload);

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Stream started confirmation timeout'));
      }, 5000);

      // Listen for streaming_active response
      const handleStreamingActive = (data: any) => {
        console.log('📡 Received streaming_active:', data);
        if (data.status === 'ACTIVE') {
          clearTimeout(timeout);
          this.socket?.off('streaming_active', handleStreamingActive);
          this.setConnectionState('ACTIVE');
          console.log('✅ Streaming is now active');
          resolve();
        }
      };

      this.socket!.on('streaming_active', handleStreamingActive);
      this.socket!.emit('stream_started', payload);
    });
  }

  /**
   * Send status update (legacy support)
   */
  async sendStatusUpdate(payload: StatusUpdateEventPayload): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    console.log(`📊 Sending status update: ${payload.status}`);
    this.socket.emit('status_update', payload);
  }



  /**
   * Send stream cancelled event
   */
  async sendStreamCancelled(sessionId: string, reason: string = 'user_cancelled'): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    const payload = {
      session_id: sessionId,
      reason: reason
    };

    console.log('❌ Sending stream_cancelled:', payload);
    this.socket.emit('stream_cancelled', payload);
    this.setConnectionState('CANCELLED');
  }

  /**
   * Send recording completed event (legacy support)
   */
  async sendRecordingCompleted(payload: RecordingCompletedEventPayload): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    console.log('🏁 Sending recording completed (legacy):', payload);
    this.socket.emit('recording_completed', payload);
    this.setConnectionState('COMPLETED');
  }

  /**
   * Send recording cancelled event (legacy support)
   */
  async sendRecordingCancelled(payload: StatusUpdateEventPayload): Promise<void> {
    if (!this.socket || !this.socket.connected) {
      throw new Error('Socket not connected');
    }

    console.log('❌ Sending recording cancelled (legacy):', payload);
    this.socket.emit('recording_cancelled', payload);
    this.setConnectionState('CANCELLED');
  }

  /**
   * Get current connection state
   */
  getConnectionState(): SocketConnectionState {
    return this.connectionState;
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.socket?.connected === true && this.connectionState !== 'DISCONNECTED';
  }

  /**
   * Get session ID
   */
  getSessionId(): string | null {
    return socketAuthService.getSessionId();
  }

  /**
   * Register event listeners
   */
  on(event: string, handler: (data: any) => void): void {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(handler);
  }

  /**
   * Remove event listeners
   */
  off(event: string, handler?: (data: any) => void): void {
    if (!this.eventHandlers[event]) {
      return;
    }

    if (!handler) {
      delete this.eventHandlers[event];
    } else {
      this.eventHandlers[event] = this.eventHandlers[event].filter(h => h !== handler);
    }
  }

  /**
   * Set connection state and trigger state change events
   * @private
   */
  private setConnectionState(state: SocketConnectionState): void {
    if (this.connectionState !== state) {
      const previousState = this.connectionState;
      this.connectionState = state;
      console.log(`🔄 State changed: ${previousState} → ${state}`);
      this.triggerEvent('state_change', { previousState, currentState: state });
    }
  }

  /**
   * Trigger event handlers
   * @private
   */
  private triggerEvent(event: string, data: any = null): void {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in ${event} handler:`, error);
        }
      });
    }
  }
}

// Export singleton instance
export const newSocketClient = new NewSocketClientImpl();

// Export class for testing and custom instances
export { NewSocketClientImpl };
