/**
 * HTTP Authentication Service for Socket.IO connections
 * Handles the initial HTTP authentication before WebSocket connection
 * Updated to follow the new API specification
 */

import { httpClient } from './httpBase';
import { BASE_API_URL } from './Routes';
import type {
  SocketAuthRequest,
  SocketAuthResponse,
  SocketAuthError,
  SocketAuthService
} from '../types/socketTypes';

/**
 * Socket.IO HTTP Authentication Service Implementation
 * Updated for new API specification
 */
class SocketAuthServiceImpl implements SocketAuthService {
  private sessionToken: string | null = null;
  private sessionId: string | null = null;
  private websocketUrl: string | null = null;
  private expiresAt: Date | null = null;
  private configuration: any = null;
  private status: string | null = null;
  private instructions: any = null;

  /**
   * Authenticate with the server and get session credentials
   * @param token - Bearer authentication token
   * @param metadata - Optional metadata for the connection
   * @returns Promise resolving to session credentials
   */
  async authenticate(token: string, metadata?: Record<string, any>): Promise<SocketAuthResponse> {
    try {
      console.log('🔐 Starting HTTP authentication for Socket.IO connection');

      // Prepare request payload
      const requestPayload: SocketAuthRequest = {
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          client_type: 'web',
          user_agent: navigator.userAgent
        }
      };

      // Make HTTP POST request to authentication endpoint (updated endpoint)
      const response = await httpClient.post<SocketAuthResponse>(
        '/socket/connect',
        requestPayload,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000 // 10 second timeout
        }
      );

      // Validate response
      if (!response.session_token || !response.session_id || !response.websocket_url) {
        throw new Error('Invalid authentication response: missing required fields');
      }

      // Store session credentials
      this.sessionToken = response.session_token;
      this.sessionId = response.session_id;
      this.websocketUrl = response.websocket_url;
      this.configuration = response.configuration;
      this.status = response.status;
      this.instructions = response.instructions;

      // Calculate expiration time from expires_at field
      if (response.expires_at) {
        this.expiresAt = new Date(response.expires_at);
      }

      console.log('✅ HTTP authentication successful');
      console.log(`📋 Session ID: ${this.sessionId}`);
      console.log(`🔗 WebSocket URL: ${this.websocketUrl}`);
      console.log(`📊 Status: ${this.status}`);
      console.log(`⚙️ Configuration:`, this.configuration);
      console.log(`⏰ Expires: ${this.expiresAt?.toISOString() || 'No expiration'}`);
      console.log('🔍 Full auth response:', response);

      return response;
    } catch (error: any) {
      console.error('❌ HTTP authentication failed:', error);

      // Clear any stored credentials on failure
      this.clearSession();

      // Handle specific error cases
      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;

        switch (status) {
          case 401:
            throw new Error('Authentication failed: Invalid or expired token');
          case 400:
            throw new Error(`Bad request: ${data?.message || 'Invalid request format'}`);
          case 429:
            throw new Error('Rate limit exceeded: Too many connection attempts');
          case 500:
            throw new Error('Server error: Please try again later');
          default:
            throw new Error(`HTTP ${status}: ${data?.message || 'Authentication failed'}`);
        }
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Authentication timeout: Please check your connection');
      } else if (error.code === 'NETWORK_ERROR') {
        throw new Error('Network error: Please check your internet connection');
      } else {
        throw new Error(`Authentication failed: ${error.message || 'Unknown error'}`);
      }
    }
  }

  /**
   * Check if current session is authenticated and not expired
   * @returns True if authenticated and session is valid
   */
  isAuthenticated(): boolean {
    if (!this.sessionToken || !this.sessionId || !this.websocketUrl) {
      return false;
    }

    // Check expiration if set
    if (this.expiresAt && new Date() >= this.expiresAt) {
      console.warn('⚠️ Session expired, clearing credentials');
      this.clearSession();
      return false;
    }

    return true;
  }

  /**
   * Get current session token
   * @returns Session token or null if not authenticated
   */
  getSessionToken(): string | null {
    return this.isAuthenticated() ? this.sessionToken : null;
  }

  /**
   * Get current session ID
   * @returns Session ID or null if not authenticated
   */
  getSessionId(): string | null {
    return this.isAuthenticated() ? this.sessionId : null;
  }

  /**
   * Get WebSocket URL
   * @returns WebSocket URL or null if not authenticated
   */
  getWebSocketUrl(): string | null {
    return this.isAuthenticated() ? this.websocketUrl : null;
  }

  /**
   * Get configuration from authentication response
   * @returns Configuration object or null if not authenticated
   */
  getConfiguration(): any {
    return this.isAuthenticated() ? this.configuration : null;
  }

  /**
   * Get status from authentication response
   * @returns Status string or null if not authenticated
   */
  getStatus(): string | null {
    return this.isAuthenticated() ? this.status : null;
  }

  /**
   * Get instructions from authentication response
   * @returns Instructions object or null if not authenticated
   */
  getInstructions(): any {
    return this.isAuthenticated() ? this.instructions : null;
  }

  /**
   * Clear stored session credentials
   * @private
   */
  private clearSession(): void {
    this.sessionToken = null;
    this.sessionId = null;
    this.websocketUrl = null;
    this.expiresAt = null;
    this.configuration = null;
    this.status = null;
    this.instructions = null;
  }

  /**
   * Get authentication token from localStorage
   * Uses 'token' field from user data as specified in requirements
   * @returns Authentication token or null if not found
   */
  static getTokenFromStorage(): string | null {
    try {
      const userJson = localStorage.getItem('nepali_user');
      if (!userJson) {
        console.warn('No user data found in localStorage');
        return null;
      }

      const user = JSON.parse(userJson);

      // Use 'token' field as specified in requirements, not 'access_token'
      const token = user.token;

      if (!token) {
        console.warn('No token found in user data');
        console.log('Available keys in user object:', Object.keys(user));
        return null;
      }

      return token;
    } catch (error) {
      console.error('Error getting token from localStorage:', error);
      return null;
    }
  }

  /**
   * Validate token format
   * @param token - Token to validate
   * @returns True if token format is valid
   */
  static validateToken(token: string): boolean {
    if (!token || typeof token !== 'string') {
      return false;
    }

    // Basic token validation - should be non-empty and reasonable length
    if (token.length < 10 || token.length > 2048) {
      return false;
    }

    return true;
  }

  /**
   * Create authentication service with automatic token retrieval
   * @param customToken - Optional custom token to use instead of localStorage
   * @returns Promise resolving to authenticated service instance
   */
  static async createAuthenticated(customToken?: string): Promise<SocketAuthServiceImpl> {
    const service = new SocketAuthServiceImpl();

    // Get token from parameter or localStorage
    const token = customToken || SocketAuthServiceImpl.getTokenFromStorage();

    if (!token) {
      throw new Error('No authentication token available');
    }

    if (!SocketAuthServiceImpl.validateToken(token)) {
      throw new Error('Invalid token format');
    }

    // Authenticate with the server
    await service.authenticate(token);

    return service;
  }
}

// Export singleton instance
export const socketAuthService = new SocketAuthServiceImpl();

// Export class for testing and custom instances
export { SocketAuthServiceImpl };

// Export static methods for convenience
export const getTokenFromStorage = SocketAuthServiceImpl.getTokenFromStorage;
export const validateToken = SocketAuthServiceImpl.validateToken;
export const createAuthenticatedService = SocketAuthServiceImpl.createAuthenticated;
