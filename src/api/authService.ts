
import { UserData, LoginResponse, SignupResponse, SignupData, OnboardingData, OnboardingResponse } from "../types/User";
import { BaseService } from "./baseService";
import { ApiCallbacks } from "./httpBase";
import { AUTH_ROUTES } from "./Routes";

/**
 * Authentication service
 */
class AuthService extends BaseService {
  /**
   * Validate tenant by slug
   * @param slug The tenant slug to validate
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with boolean indicating if tenant is valid
   */
  async validateTenant(
    slug: string,
    callbacks?: ApiCallbacks<{ tenant_id: string }>
  ): Promise<boolean> {
    try {
      const data = await this.get<{ tenant_id: string }>(
        `${AUTH_ROUTES.VALIDATE_TENANT}?slug=${slug}`,
        undefined,
        callbacks
      );
      return !!data.tenant_id;
    } catch (error) {
      console.error("Error validating tenant:", error);
      return false;
    }
  }

  /**
   * Login user
   * @param username The username
   * @param password The password
   * @param tenantSlug The tenant slug
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with login response
   */
  async loginUser(
    username: string,
    password: string,
    tenantSlug: string,
    callbacks?: ApiCallbacks
  ): Promise<LoginResponse> {
    try {
      // Create URL-encoded form data as specified in the API requirements
      const formData = new URLSearchParams();
      formData.append('grant_type', 'password');
      formData.append('username', username);
      formData.append('password', password);
      formData.append('scope', '');
      formData.append('client_id', tenantSlug);

      // Use post method with custom config for form data
      const data = await this.post(
        AUTH_ROUTES.LOGIN,
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          }
        },
        callbacks
      );

      // Transform backend response to match our UserData type
      const userData: UserData = {
        id: data.id,
        username: data.username,
        email: data.username, // Using username as email since the backend doesn't return email
        role: data.role,
        token: data.access_token,
        tenantSlug: data.tenant_slug,
        tenantId: data.tenant_id,
        tenantLabel: data.tenant_label,
        tokenType: data.token_type,
        virtueProjectNameId: data.tenant_id // Using tenant_id as virtueProjectNameId for now
      };

      return {
        success: true,
        message: 'Login successful',
        user: userData
      };
    } catch (error) {
      console.error("Login error:", error);
      // Handle error object that might contain validation errors
      let errorMessage = 'An error occurred during login';

      if (error.response && error.response.data) {
        // Check if the error response has a detail field that might be an array or object
        const detail = error.response.data.detail;

        if (detail) {
          if (Array.isArray(detail)) {
            // If it's an array of validation errors, get the first one's message
            errorMessage = detail[0]?.msg || errorMessage;
          } else if (typeof detail === 'object') {
            // If it's an object with a msg field
            errorMessage = detail.msg || errorMessage;
          } else if (typeof detail === 'string') {
            // If it's a simple string
            errorMessage = detail;
          }
        } else if (error.response.data.message) {
          // Some APIs return errors in a message field
          errorMessage = error.response.data.message;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage,
        user: null
      };
    }
  }

  /**
   * Signup user
   * @param signupData The signup data (username, email, password, full_name, client_id)
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with signup response
   */
  async signupUser(
    signupData: SignupData,
    callbacks?: ApiCallbacks
  ): Promise<SignupResponse> {
    try {
      // Create URL-encoded form data as specified in the API requirements
      const formData = new URLSearchParams();
      formData.append('username', signupData.username);
      formData.append('email', signupData.email);
      formData.append('password', signupData.password);
      formData.append('client_id', signupData.client_id);

      // Add optional full_name if provided
      if (signupData.full_name) {
        formData.append('full_name', signupData.full_name);
      }

      // Use post method with form data
      const data = await this.post(
        AUTH_ROUTES.SIGNUP,
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          }
        },
        callbacks
      );

      // Transform backend response to match our UserData type if access_token is provided
      let userData: UserData | undefined;
      if (data.access_token) {
        userData = {
          id: data.id || data.user_id,
          username: data.username,
          email: data.email,
          role: data.role || 'user',
          token: data.access_token,
          tenantSlug: data.tenant_slug,
          tenantId: data.tenant_id,
          tenantLabel: data.tenant_label,
          tokenType: data.token_type || 'Bearer',
          virtueProjectNameId: data.tenant_id,
          full_name: data.full_name
        };
      }

      return {
        success: true,
        message: 'Signup successful',
        user_id: data.user_id,
        username: data.username,
        email: data.email,
        user: userData
      };
    } catch (error) {
      console.error("Signup error:", error);
      // Handle error object that might contain validation errors
      let errorMessage = 'An error occurred during signup';

      if (error.response && error.response.data) {
        // Check if the error response has a detail field that might be an array or object
        const detail = error.response.data.detail;

        if (detail) {
          if (Array.isArray(detail)) {
            // If it's an array of validation errors, get the first one's message
            errorMessage = detail[0]?.msg || errorMessage;
          } else if (typeof detail === 'object') {
            // If it's an object with a msg field
            errorMessage = detail.msg || errorMessage;
          } else if (typeof detail === 'string') {
            // If it's a simple string
            errorMessage = detail;
          }
        } else if (error.response.data.message) {
          // Some APIs return errors in a message field
          errorMessage = error.response.data.message;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  }

  // We're now using the client ID directly from environment variables
  // so we don't need to fetch it from the backend

  // Google authentication is now handled by the GoogleAuth component using @react-oauth/google

  /**
   * Handle Google OAuth callback
   * @param codeOrCredential The authorization code or credential from Google
   * @param tenantSlug The tenant slug
   * @param isCredential Whether the first parameter is a credential (true) or code (false)
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with login response
   */
  async handleGoogleCallback(
    codeOrCredential: string,
    tenantSlug: string,
    isCredential: boolean = false,
    callbacks?: ApiCallbacks
  ): Promise<LoginResponse> {
    try {
      console.log('handleGoogleCallback called with:', {
        codeOrCredential: codeOrCredential.substring(0, 20) + '...',
        tenantSlug,
        isCredential
      });

      // For credential (ID token), use the main Google auth endpoint
      // For code, use the callback endpoint
      const endpoint = isCredential
        ? AUTH_ROUTES.GOOGLE_AUTH
        : `${AUTH_ROUTES.GOOGLE_AUTH}/callback`;

      console.log('Using endpoint:', endpoint);

      // According to the documentation, for ID token we use id_token
      const payload = isCredential
        ? { id_token: codeOrCredential, client_id: tenantSlug }
        : { code: codeOrCredential, client_id: tenantSlug };

      console.log('Sending payload:', {
        ...payload,
        id_token: payload.id_token ? payload.id_token.substring(0, 20) + '...' : undefined,
        code: payload.code ? payload.code.substring(0, 20) + '...' : undefined
      });

      const data = await this.post(
        endpoint,
        payload,
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        },
        callbacks
      );

      // Transform backend response to match our UserData type
      const userData: UserData = {
        id: data.id,
        username: data.username,
        email: data.email || data.username,
        role: data.role,
        token: data.access_token,
        tenantSlug: data.tenant_slug,
        tenantId: data.tenant_id,
        tenantLabel: data.tenant_label,
        tokenType: data.token_type,
        virtueProjectNameId: data.tenant_id,
        full_name: data.full_name
      };

      return {
        success: true,
        message: 'Google authentication successful',
        user: userData
      };
    } catch (error) {
      console.error("Google authentication error:", error);
      // Handle error object that might contain validation errors
      let errorMessage = 'An error occurred during Google authentication';

      if (error.response && error.response.data) {
        // Check if the error response has a detail field that might be an array or object
        const detail = error.response.data.detail;

        if (detail) {
          if (Array.isArray(detail)) {
            // If it's an array of validation errors, get the first one's message
            errorMessage = detail[0]?.msg || errorMessage;
          } else if (typeof detail === 'object') {
            // If it's an object with a msg field
            errorMessage = detail.msg || errorMessage;
          } else if (typeof detail === 'string') {
            // If it's a simple string
            errorMessage = detail;
          }
        } else if (error.response.data.message) {
          // Some APIs return errors in a message field
          errorMessage = error.response.data.message;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage,
        user: null
      };
    }
  }

  /**
   * Submit user onboarding data
   * @param onboardingData The onboarding data (age, difficulty_level, preferred_topics)
   * @param callbacks Optional callbacks for success, error, and finally
   * @returns Promise with onboarding response
   */
  async submitOnboarding(
    onboardingData: OnboardingData,
    callbacks?: ApiCallbacks
  ): Promise<OnboardingResponse> {
    try {
      // Use post method with JSON data
      const data = await this.post(
        AUTH_ROUTES.ONBOARDING,
        onboardingData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        },
        callbacks
      );

      return {
        success: true,
        message: 'Onboarding completed successfully'
      };
    } catch (error) {
      console.error("Onboarding error:", error);
      let errorMessage = 'An error occurred during onboarding';

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        if (axiosError.response?.data?.detail) {
          errorMessage = axiosError.response.data.detail;
        } else if (axiosError.response?.data?.message) {
          errorMessage = axiosError.response.data.message;
        }
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  }
}

// Export a singleton instance
export const authService = new AuthService();

// Export convenience functions
export const validateTenant = (slug: string, callbacks?: ApiCallbacks<{ tenant_id: string }>) =>
  authService.validateTenant(slug, callbacks);

export const loginUser = (username: string, password: string, tenantSlug: string, callbacks?: ApiCallbacks) =>
  authService.loginUser(username, password, tenantSlug, callbacks);

export const signupUser = (signupData: SignupData, callbacks?: ApiCallbacks) =>
  authService.signupUser(signupData, callbacks);

export const submitOnboarding = (onboardingData: OnboardingData, callbacks?: ApiCallbacks) =>
  authService.submitOnboarding(onboardingData, callbacks);

// Google authentication is handled by the GoogleAuth component

export const handleGoogleCallback = (
  codeOrCredential: string,
  tenantSlug: string,
  isCredential: boolean = false,
  callbacks?: ApiCallbacks
) => authService.handleGoogleCallback(codeOrCredential, tenantSlug, isCredential, callbacks);
