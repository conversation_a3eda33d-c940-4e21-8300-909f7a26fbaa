import React, { ReactNode } from 'react';
import { AnimatePresence } from 'framer-motion';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import SideNavigation from '@/features/navigation/components/SideNavigation';
import SidebarCollapseButton from '@/components/navigation/SidebarCollapseButton';
import Breadcrumbs from '@/components/navigation/Breadcrumbs';
import { useTheme } from '@/core/hooks/useTheme';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
  showBreadcrumbs?: boolean;
}

/**
 * Main layout component for authenticated pages
 */
const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  title,
  description,
  showBreadcrumbs = true,
}) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  return (
    <SidebarProvider>
      <div className={cn(
        'flex min-h-screen w-full',
        isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
      )}>
        <SideNavigation />
        <SidebarCollapseButton />
        <SidebarInset>
          <div className="flex flex-col h-screen">
            {/* Page header with left padding to ensure content appears to the right of collapse button */}
            <div className={cn(
              'shadow p-2 pl-14',
              isDarkMode ? 'bg-gray-800 text-white' : 'bg-white'
            )}>
              <div className="flex items-center justify-between">
                <div>
                  {title && (
                    <h1 className={cn(
                      'text-lg font-bold',
                      isDarkMode ? 'text-blue-400' : 'text-blue-700'
                    )}>
                      {title}
                    </h1>
                  )}
                  {description && (
                    <p className="text-muted-foreground text-sm">
                      {description}
                    </p>
                  )}
                  {showBreadcrumbs && (
                    <Breadcrumbs className="mt-1" />
                  )}
                </div>
              </div>
            </div>

            {/* Page content */}
            <main className="flex-1 p-3 overflow-auto">
              <AnimatePresence mode="wait">
                {children}
              </AnimatePresence>
            </main>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
