import React from 'react';
import { Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/core/hooks/useTheme';
import { motion } from 'framer-motion';

interface ThemeToggleProps {
  className?: string;
}

/**
 * Enhanced theme toggle component with animations
 */
export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className }) => {
  const { theme, setTheme } = useTheme();
  const isDarkMode = theme === 'dark';

  const toggleMode = () => {
    setTheme(isDarkMode ? 'light' : 'dark');
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleMode}
      className={`${className} ${!isDarkMode ? 'bg-gray-100 hover:bg-gray-200' : ''}`}
      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <motion.div
        initial={{ rotate: 0 }}
        animate={{ rotate: isDarkMode ? 180 : 0 }}
        transition={{ duration: 0.5, ease: 'easeInOut' }}
        className="relative w-5 h-5"
      >
        {isDarkMode ? (
          <Moon className="absolute inset-0 h-5 w-5 rotate-90 transition-all dark:rotate-0" />
        ) : (
          <Sun className="absolute inset-0 h-5 w-5 rotate-0 transition-all dark:-rotate-90 text-primary" />
        )}
      </motion.div>
    </Button>
  );
};

export default ThemeToggle;
