import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { pageTransitionVariants, TransitionDirection, transitionTimings } from '@/core/animations/transitions';
import { cn } from '@/lib/utils';
import { useTheme } from '@/core/hooks/useTheme';

interface AnimatedPageProps {
  children: ReactNode;
  className?: string;
  direction?: TransitionDirection;
  delay?: number;
  duration?: 'fast' | 'normal' | 'slow';
}

/**
 * Enhanced animated page component for smooth page transitions
 */
export const AnimatedPage: React.FC<AnimatedPageProps> = ({
  children,
  className,
  direction = 'up',
  delay = 0,
  duration = 'normal',
}) => {
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';
  const variants = pageTransitionVariants(direction);
  const timing = transitionTimings[duration];

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={variants}
      transition={{
        ...timing,
        delay,
      }}
      className={cn(
        'w-full transition-colors duration-300',
        isDarkMode ? 'text-gray-100' : 'text-gray-900',
        className
      )}
    >
      {children}
    </motion.div>
  );
};

/**
 * Enhanced staggered children component for animating lists of items
 */
export const StaggeredChildren: React.FC<{
  children: ReactNode;
  className?: string;
  staggerDelay?: number;
  initialDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}> = ({
  children,
  className,
  staggerDelay = 0.1,
  initialDelay = 0,
  direction = 'up'
}) => {
  // Define direction-based variants
  const getChildVariants = () => {
    switch (direction) {
      case 'up':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: 20 }
        };
      case 'down':
        return {
          initial: { opacity: 0, y: -20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: -20 }
        };
      case 'left':
        return {
          initial: { opacity: 0, x: 20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: 20 }
        };
      case 'right':
        return {
          initial: { opacity: 0, x: -20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: -20 }
        };
    }
  };

  const childVariants = getChildVariants();

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={{
        initial: { opacity: 0 },
        animate: {
          opacity: 1,
          transition: {
            when: 'beforeChildren',
            staggerChildren: staggerDelay,
            delayChildren: initialDelay
          }
        },
        exit: {
          opacity: 0,
          transition: {
            when: 'afterChildren',
            staggerChildren: staggerDelay / 2,
            staggerDirection: -1
          }
        }
      }}
    >
      {React.Children.map(children, (child, i) => (
        <motion.div
          key={i}
          variants={childVariants}
          transition={{
            duration: 0.4,
            ease: 'easeOut',
            type: "spring",
            stiffness: 100,
            damping: 15
          }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

export default AnimatedPage;
