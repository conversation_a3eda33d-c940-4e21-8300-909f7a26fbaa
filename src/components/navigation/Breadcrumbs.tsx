import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { getBreadcrumbs } from '@/routes';

interface BreadcrumbsProps {
  className?: string;
}

/**
 * Breadcrumbs component for navigation context
 */
const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ className }) => {
  const location = useLocation();
  const breadcrumbs = getBreadcrumbs(location.pathname);

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, x: -10 },
    show: { opacity: 1, x: 0 },
  };

  return (
    <motion.nav
      aria-label="Breadcrumbs"
      className={cn('flex items-center text-sm text-muted-foreground', className)}
      variants={container}
      initial="hidden"
      animate="show"
    >
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((breadcrumb, index) => (
          <React.Fragment key={`${breadcrumb.path}-${index}`}>
            {index > 0 && (
              <li className="flex items-center">
                <ChevronRight className="h-4 w-4" />
              </li>
            )}
            <motion.li variants={item}>
              {index === breadcrumbs.length - 1 ? (
                <span className="font-medium text-foreground">
                  {breadcrumb.title}
                </span>
              ) : (
                <Link
                  to={breadcrumb.path}
                  className="hover:text-foreground transition-colors"
                >
                  {breadcrumb.title}
                </Link>
              )}
            </motion.li>
          </React.Fragment>
        ))}
      </ol>
    </motion.nav>
  );
};

export default Breadcrumbs;
