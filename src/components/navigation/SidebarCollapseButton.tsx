import React from 'react';
import { ChevronRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { useSidebar } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';

interface SidebarCollapseButtonProps {
  className?: string;
}

/**
 * A button that appears on the left side of the screen when the sidebar is collapsed
 * Used to expand the sidebar
 */
const SidebarCollapseButton: React.FC<SidebarCollapseButtonProps> = ({ className }) => {
  const { state, toggleSidebar } = useSidebar();
  const isCollapsed = state === 'collapsed';

  // Only show when sidebar is collapsed
  if (!isCollapsed) return null;

  return (
    <motion.button
      onClick={toggleSidebar}
      className={cn(
        "fixed left-0 top-4 z-50 flex items-center justify-center h-10 w-10 bg-white dark:bg-gray-800 rounded-r-md shadow-lg ml-1 border border-gray-300 dark:border-gray-700",
        "hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-800 dark:text-white transition-all",
        className
      )}
      initial={{ x: -100 }}
      animate={{ x: 0 }}
      exit={{ x: -100 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      whileHover={{ scale: 1.1, x: 2 }}
      whileTap={{ scale: 0.95 }}
      aria-label="Expand sidebar"
    >
      <ChevronRight className="h-6 w-6 text-gray-800 dark:text-white" />
    </motion.button>
  );
};

export default SidebarCollapseButton;
