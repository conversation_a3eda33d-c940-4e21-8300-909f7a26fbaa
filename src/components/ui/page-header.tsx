import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { SidebarTrigger } from '@/components/ui/sidebar';
import ThemeToggle from '@/components/ui/theme-toggle';
import { useThemeMode } from '@/hooks/useThemeMode';

interface PageHeaderProps {
  title: string;
  description?: string;
  className?: string;
  actions?: React.ReactNode;
  titleColor?: string;
  showThemeToggle?: boolean;
}

/**
 * Page header component for consistent page headers
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  className,
  actions,
  titleColor,
  showThemeToggle = true
}) => {
  const { isDarkMode } = useThemeMode();

  // Default title colors for different pages
  const getTitleColor = () => {
    if (titleColor) return titleColor;
    
    if (title.toLowerCase().includes('dashboard')) return 'text-nepali-maroon';
    if (title.toLowerCase().includes('task')) return 'text-purple-700';
    if (title.toLowerCase().includes('learning') || title.toLowerCase().includes('audio')) return 'text-nepali-red';
    if (title.toLowerCase().includes('user')) return 'text-blue-700';
    
    return 'text-primary';
  };

  return (
    <motion.div 
      className={cn(
        `${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white'} shadow p-4`,
        className
      )}
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between">
        <div>
          <motion.h1 
            className={cn(
              "text-2xl font-bold",
              isDarkMode && getTitleColor().includes('text-') 
                ? getTitleColor().replace('text-', 'text-') + '/90' 
                : getTitleColor()
            )}
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.3 }}
          >
            {title}
          </motion.h1>
          
          {description && (
            <motion.p 
              className={`${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              {description}
            </motion.p>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {actions}
          {showThemeToggle && <ThemeToggle className="mr-2" />}
          <SidebarTrigger />
        </div>
      </div>
    </motion.div>
  );
};

export default PageHeader;
