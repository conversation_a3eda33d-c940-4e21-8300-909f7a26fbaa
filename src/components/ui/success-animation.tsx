import React from 'react';
import { motion, AnimationControls } from 'framer-motion';
import { CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SuccessAnimationProps {
  className?: string;
  controls?: AnimationControls;
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Success animation component for showing success state
 */
export const SuccessAnimation: React.FC<SuccessAnimationProps> = ({
  className,
  controls,
  message = 'Success!',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };
  
  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={controls || { opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={cn(
        'flex items-center justify-center gap-2 rounded-md bg-green-50 p-3 text-green-700 dark:bg-green-900/20 dark:text-green-400',
        sizeClasses[size],
        className
      )}
    >
      <motion.div
        initial={{ rotate: 0 }}
        animate={{ rotate: [0, 15, -15, 0] }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <CheckCircle className={iconSizes[size]} />
      </motion.div>
      <span>{message}</span>
    </motion.div>
  );
};

/**
 * Success confetti animation component
 */
export const SuccessConfetti: React.FC<{ className?: string }> = ({ className }) => {
  // Generate random confetti pieces
  const confettiPieces = Array.from({ length: 50 }).map((_, i) => {
    const x = Math.random() * 400 - 200;
    const y = Math.random() * -300;
    const rotation = Math.random() * 360;
    const scale = Math.random() * 0.5 + 0.5;
    const duration = Math.random() * 1 + 1;
    const delay = Math.random() * 0.3;
    
    return (
      <motion.div
        key={i}
        className="absolute h-2 w-2 rounded-sm"
        style={{
          backgroundColor: [
            '#34d399', // green
            '#60a5fa', // blue
            '#f472b6', // pink
            '#fbbf24', // yellow
            '#a78bfa'  // purple
          ][Math.floor(Math.random() * 5)],
          top: 0,
          left: '50%',
          zIndex: 50
        }}
        initial={{ x: 0, y: 0, rotate: 0, scale: 0 }}
        animate={{
          x,
          y: y + 300,
          rotate: rotation,
          scale
        }}
        transition={{
          duration,
          delay,
          ease: 'easeOut'
        }}
      />
    );
  });

  return (
    <motion.div
      className={cn('absolute inset-0 overflow-hidden pointer-events-none', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {confettiPieces}
    </motion.div>
  );
};

export default SuccessAnimation;
