import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { X, ChevronLeft, ChevronRight, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TourStep } from '@/hooks/useGuidedTour';

interface GuidedTourProps {
  isOpen: boolean;
  steps: TourStep[];
  currentStep: number;
  onNext: () => void;
  onPrev: () => void;
  onClose: () => void;
  onComplete: () => void;
}

/**
 * Guided tour component for showing interactive tours
 */
export const GuidedTour: React.FC<GuidedTourProps> = ({
  isOpen,
  steps,
  currentStep,
  onNext,
  onPrev,
  onClose,
  onComplete
}) => {
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const [tooltipRect, setTooltipRect] = useState<DOMRect | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  
  const currentStepData = steps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;
  
  // Update target element position
  useEffect(() => {
    if (!isOpen || !currentStepData) return;
    
    const updatePosition = () => {
      const targetElement = document.querySelector(currentStepData.target);
      if (targetElement) {
        setTargetRect(targetElement.getBoundingClientRect());
      }
      
      if (tooltipRef.current) {
        setTooltipRect(tooltipRef.current.getBoundingClientRect());
      }
    };
    
    updatePosition();
    
    // Update position on resize
    window.addEventListener('resize', updatePosition);
    
    return () => {
      window.removeEventListener('resize', updatePosition);
    };
  }, [isOpen, currentStep, currentStepData]);
  
  // Calculate tooltip position
  const getTooltipPosition = () => {
    if (!targetRect || !tooltipRect) {
      return { top: 0, left: 0 };
    }
    
    const placement = currentStepData.placement || 'bottom';
    const margin = 10; // Margin between target and tooltip
    
    let top = 0;
    let left = 0;
    
    switch (placement) {
      case 'top':
        top = targetRect.top - tooltipRect.height - margin;
        left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
        break;
      case 'right':
        top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2);
        left = targetRect.right + margin;
        break;
      case 'bottom':
        top = targetRect.bottom + margin;
        left = targetRect.left + (targetRect.width / 2) - (tooltipRect.width / 2);
        break;
      case 'left':
        top = targetRect.top + (targetRect.height / 2) - (tooltipRect.height / 2);
        left = targetRect.left - tooltipRect.width - margin;
        break;
    }
    
    // Ensure tooltip stays within viewport
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    if (left < 0) left = margin;
    if (left + tooltipRect.width > viewportWidth) left = viewportWidth - tooltipRect.width - margin;
    if (top < 0) top = margin;
    if (top + tooltipRect.height > viewportHeight) top = viewportHeight - tooltipRect.height - margin;
    
    return { top, left };
  };
  
  // Don't render if not open
  if (!isOpen) return null;
  
  const tooltipPosition = getTooltipPosition();
  
  return (
    <div className="fixed inset-0 z-50 pointer-events-none">
      {/* Overlay */}
      {!currentStepData.disableOverlay && (
        <div className="absolute inset-0 bg-black/50 pointer-events-auto" onClick={onClose} />
      )}
      
      {/* Highlight target element */}
      {targetRect && !currentStepData.disableOverlay && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute bg-transparent border-2 border-primary rounded-md pointer-events-none"
          style={{
            top: targetRect.top - 4,
            left: targetRect.left - 4,
            width: targetRect.width + 8,
            height: targetRect.height + 8,
            boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)'
          }}
        />
      )}
      
      {/* Tooltip */}
      <AnimatePresence>
        <motion.div
          ref={tooltipRef}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="absolute pointer-events-auto"
          style={{
            top: tooltipPosition.top,
            left: tooltipPosition.left,
            maxWidth: '350px',
            zIndex: 60
          }}
        >
          <Card className="shadow-lg border-primary/20">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center gap-2">
                  <HelpCircle className="h-5 w-5 text-primary" />
                  {currentStepData.title}
                </CardTitle>
                <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <p>{currentStepData.content}</p>
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-4">
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                {currentStep + 1} of {steps.length}
              </div>
              <div className="flex gap-2">
                {!isFirstStep && (
                  <Button variant="outline" size="sm" onClick={onPrev}>
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Back
                  </Button>
                )}
                <Button size="sm" onClick={isLastStep ? onComplete : onNext}>
                  {isLastStep ? 'Finish' : 'Next'}
                  {!isLastStep && <ChevronRight className="h-4 w-4 ml-1" />}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default GuidedTour;
