import React from 'react';
import { Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useThemeMode } from '@/hooks/useThemeMode';
import { motion } from 'framer-motion';

interface ThemeToggleProps {
  className?: string;
}

/**
 * Theme toggle component for switching between light and dark mode
 */
export const ThemeToggle: React.FC<ThemeToggleProps> = ({ className }) => {
  const { toggleTheme, isDarkMode } = useThemeMode();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className={className}
      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <motion.div
        initial={{ rotate: 0 }}
        animate={{ rotate: isDarkMode ? 180 : 0 }}
        transition={{ duration: 0.5, ease: 'easeInOut' }}
        className="relative w-5 h-5"
      >
        {isDarkMode ? (
          <Moon className="absolute inset-0 h-5 w-5 rotate-90 transition-all dark:rotate-0" />
        ) : (
          <Sun className="absolute inset-0 h-5 w-5 rotate-0 transition-all dark:-rotate-90" />
        )}
      </motion.div>
    </Button>
  );
};

export default ThemeToggle;
