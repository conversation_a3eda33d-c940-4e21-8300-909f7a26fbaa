import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
}

/**
 * Page transition component for smooth page transitions
 */
export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
  delay = 0,
  direction = 'up'
}) => {
  // Define initial and animate values based on direction
  const getVariants = () => {
    switch (direction) {
      case 'up':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 }
        };
      case 'down':
        return {
          initial: { opacity: 0, y: -20 },
          animate: { opacity: 1, y: 0 }
        };
      case 'left':
        return {
          initial: { opacity: 0, x: 20 },
          animate: { opacity: 1, x: 0 }
        };
      case 'right':
        return {
          initial: { opacity: 0, x: -20 },
          animate: { opacity: 1, x: 0 }
        };
      case 'none':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 }
        };
    }
  };

  const variants = getVariants();

  return (
    <motion.div
      initial={variants.initial}
      animate={variants.animate}
      exit={variants.initial}
      transition={{
        duration: 0.4,
        ease: 'easeOut',
        delay: delay
      }}
      className={cn('w-full', className)}
    >
      {children}
    </motion.div>
  );
};

/**
 * Staggered children component for animating lists of items
 */
export const StaggeredChildren: React.FC<{
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  initialDelay?: number;
}> = ({
  children,
  className,
  staggerDelay = 0.1,
  initialDelay = 0
}) => {
  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            when: 'beforeChildren',
            staggerChildren: staggerDelay,
            delayChildren: initialDelay
          }
        }
      }}
    >
      {React.Children.map(children, (child, i) => (
        <motion.div
          key={i}
          variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0 }
          }}
          transition={{ duration: 0.4, ease: 'easeOut' }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

export default PageTransition;
