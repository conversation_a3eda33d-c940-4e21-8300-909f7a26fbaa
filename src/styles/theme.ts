/**
 * Theme configuration for the application
 * This file defines the theme tokens and configuration
 */

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';
export type ThemeColor = 'default' | 'nepali' | 'blue' | 'green' | 'purple';

// Theme interface
export interface Theme {
  name: string;
  mode: ThemeMode;
  color: ThemeColor;
}

// Default themes
export const defaultThemes: Record<ThemeColor, { light: string; dark: string }> = {
  default: {
    light: 'Default Light',
    dark: 'Default Dark',
  },
  nepali: {
    light: 'Nepali Light',
    dark: 'Nepali Dark',
  },
  blue: {
    light: 'Blue Light',
    dark: 'Blue Dark',
  },
  green: {
    light: 'Green Light',
    dark: 'Green Dark',
  },
  purple: {
    light: 'Purple Light',
    dark: 'Purple Dark',
  },
};

// Color palettes
export const colorPalettes: Record<ThemeColor, Record<string, string>> = {
  default: {
    primary: 'hsl(0, 91%, 42%)',
    secondary: 'hsl(25, 95%, 53%)',
    accent: 'hsl(210, 40%, 96.1%)',
    background: 'hsl(0, 0%, 100%)',
    foreground: 'hsl(222.2, 84%, 4.9%)',
  },
  nepali: {
    primary: 'hsl(0, 91%, 42%)',
    secondary: 'hsl(25, 95%, 53%)',
    accent: 'hsl(210, 40%, 96.1%)',
    background: 'hsl(0, 0%, 100%)',
    foreground: 'hsl(222.2, 84%, 4.9%)',
  },
  blue: {
    primary: 'hsl(210, 100%, 50%)',
    secondary: 'hsl(200, 100%, 60%)',
    accent: 'hsl(210, 40%, 96.1%)',
    background: 'hsl(0, 0%, 100%)',
    foreground: 'hsl(222.2, 84%, 4.9%)',
  },
  green: {
    primary: 'hsl(142, 76%, 36%)',
    secondary: 'hsl(160, 84%, 39%)',
    accent: 'hsl(150, 40%, 96.1%)',
    background: 'hsl(0, 0%, 100%)',
    foreground: 'hsl(222.2, 84%, 4.9%)',
  },
  purple: {
    primary: 'hsl(270, 70%, 50%)',
    secondary: 'hsl(280, 70%, 60%)',
    accent: 'hsl(270, 40%, 96.1%)',
    background: 'hsl(0, 0%, 100%)',
    foreground: 'hsl(222.2, 84%, 4.9%)',
  },
};

// CSS variable generation
export const generateThemeVariables = (mode: ThemeMode, color: ThemeColor): Record<string, string> => {
  const isDark = mode === 'dark';
  const palette = colorPalettes[color];
  
  // Base variables
  const variables: Record<string, string> = {
    '--background': isDark ? 'hsl(222.2, 84%, 4.9%)' : 'hsl(0, 0%, 100%)',
    '--foreground': isDark ? 'hsl(210, 40%, 98%)' : 'hsl(222.2, 84%, 4.9%)',
    
    '--card': isDark ? 'hsl(222.2, 84%, 4.9%)' : 'hsl(0, 0%, 100%)',
    '--card-foreground': isDark ? 'hsl(210, 40%, 98%)' : 'hsl(222.2, 84%, 4.9%)',
    
    '--popover': isDark ? 'hsl(222.2, 84%, 4.9%)' : 'hsl(0, 0%, 100%)',
    '--popover-foreground': isDark ? 'hsl(210, 40%, 98%)' : 'hsl(222.2, 84%, 4.9%)',
    
    '--primary': palette.primary,
    '--primary-foreground': isDark ? 'hsl(210, 40%, 98%)' : 'hsl(0, 0%, 100%)',
    
    '--secondary': palette.secondary,
    '--secondary-foreground': isDark ? 'hsl(210, 40%, 98%)' : 'hsl(222.2, 47.4%, 11.2%)',
    
    '--muted': isDark ? 'hsl(217.2, 32.6%, 17.5%)' : 'hsl(210, 40%, 96.1%)',
    '--muted-foreground': isDark ? 'hsl(215, 20.2%, 65.1%)' : 'hsl(215.4, 16.3%, 46.9%)',
    
    '--accent': isDark ? 'hsl(217.2, 32.6%, 17.5%)' : palette.accent,
    '--accent-foreground': isDark ? 'hsl(210, 40%, 98%)' : 'hsl(222.2, 47.4%, 11.2%)',
    
    '--destructive': isDark ? 'hsl(0, 62.8%, 30.6%)' : 'hsl(0, 84.2%, 60.2%)',
    '--destructive-foreground': isDark ? 'hsl(210, 40%, 98%)' : 'hsl(210, 40%, 98%)',
    
    '--border': isDark ? 'hsl(217.2, 32.6%, 17.5%)' : 'hsl(214.3, 31.8%, 91.4%)',
    '--input': isDark ? 'hsl(217.2, 32.6%, 17.5%)' : 'hsl(214.3, 31.8%, 91.4%)',
    '--ring': palette.primary,
    
    '--radius': '0.5rem',
  };
  
  return variables;
};

// Apply theme to document
export const applyTheme = (mode: ThemeMode, color: ThemeColor): void => {
  const root = document.documentElement;
  const variables = generateThemeVariables(mode, color);
  
  // Apply CSS variables
  Object.entries(variables).forEach(([key, value]) => {
    root.style.setProperty(key, value);
  });
  
  // Apply class for dark mode
  if (mode === 'dark' || (mode === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
};
